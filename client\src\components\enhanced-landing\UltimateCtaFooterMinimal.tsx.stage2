/**
 * Ultimate CTA + Minimal Footer Integration Component
 * Seamless integration of enhanced CTA and simple Footer with unified theming
 */

import React from 'react';
import { motion  } from 'framer-motion';
import UltimateCTA from './UltimateCTA';
import MinimalFooter from './MinimalFooter';

interface UltimateCtaFooterMinimalProps {
  
  onStartTrial?: () => void;
  onWatchDemo?: () => void;
  
};

const UltimateCtaFooterMinimal: React.FC<UltimateCtaFooterMinimalProps> = ({;
  onStartTrial,
  // onWatchDemo
}) => {
  // Default handlers if not provided
  const handleStartTrial = onStartTrial || (() => {;
    console.log('Start trial clicked')
    // You can implement default behavior here, such as:
    // - Opening a modal
    // - Redirecting to signup page
    // - Triggering analytics event
  })

  const handleWatchDemo = onWatchDemo || (() => {;
    console.log('Watch demo clicked')
    // You can implement default behavior here, such as:
    // - Opening a video modal
    // - Redirecting to demo page
    // - Triggering analytics event
  })

  return (<motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      className="relative"
    >
      {/* CTA Section */}
      <UltimateCTA
        onStartTrial={handleStartTrial}
        onWatchDemo={handleWatchDemo}
      />
      {/* Seamless Transition Element - Enhanced */}
      <div className="relative h-2">
        {/* Enhanced gradient divider that creates smooth visual flow */}
        <div 
          className="absolute inset-0" 
          style={{
            background: `linear-gradient(
              180deg, 
              rgba(245, 243, 255, 0.6) 0%,
              rgba(245, 243, 255, 0.8) 50%,
              rgba(245, 243, 255, 0.8) 100%
            )`
          }}
        />
        {/* Additional transition blur for ultra-smooth effect */}
        <div 
          className="absolute inset-0 backdrop-blur-[0.5px]"
          style={{
            background: `linear-gradient(
              180deg, 
              transparent 0%,
              rgba(255, 255, 255, 0.1) 100%
            )`
          }}
        />
      </div>
      {/* Minimal Footer Section */}
      <MinimalFooter />
    </motion.div>

};

export default UltimateCtaFooterMinimal;