import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { authTokensV2 } from '@/design-system/tokens/auth-tokens';
import { Loader2 } from 'lucide-react';

interface EnhancedAuthButtonProps extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 
  'onDrag' | 'onDragStart' | 'onDragEnd' | 'onDragEnter' | 'onDragLeave' | 'onDragOver' | 'onDrop' |
  'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration' | 'onTransitionEnd'
> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  gradient?: boolean;
  children: React.ReactNode;
}

const EnhancedAuthButton = React.memo(React.forwardRef<HTMLButtonElement, EnhancedAuthButtonProps>(
  ({ 
    variant = 'primary',
    size = 'md',
    loading = false,
    icon,
    iconPosition = 'left',
    fullWidth = true,
    gradient = false,
    className,
    children,
    disabled,
    ...props 
  }, ref) => {
    const isDisabled = disabled || loading;

    // 尺寸变体 (响应式)
    const sizeVariants = {
      sm: "py-2 px-4 text-sm rounded-lg sm:py-2.5 sm:px-5",
      md: "py-3 px-6 text-base rounded-xl sm:py-3.5 sm:px-7",
      lg: "py-4 px-8 text-lg rounded-xl sm:py-4 sm:px-10"
    };

    // 样式变体 (深色主题支持)
    const variantStyles = {
      primary: cn(
        gradient
          ? "bg-gradient-to-r from-purple-600 to-purple-700 dark:from-purple-500 dark:to-purple-600 text-white"
          : "bg-purple-600 dark:bg-purple-500 text-white",
        "hover:bg-purple-700 dark:hover:bg-purple-600 hover:shadow-lg",
        "focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800",
        "active:bg-purple-800 dark:active:bg-purple-700",
        "shadow-md hover:shadow-xl",
        "border border-transparent"
      ),
      secondary: cn(
        "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-600",
        "hover:bg-gray-200 dark:hover:bg-gray-600 hover:border-gray-300 dark:hover:border-gray-500",
        "focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800",
        "active:bg-gray-300 dark:active:bg-gray-500"
      ),
      outline: cn(
        "bg-transparent border-2 border-purple-300 dark:border-purple-500 text-purple-700 dark:text-purple-300",
        "hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:border-purple-400 dark:hover:border-purple-400",
        "focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800",
        "active:bg-purple-100 dark:active:bg-purple-900/40"
      ),
      ghost: cn(
        "bg-transparent text-gray-600 dark:text-gray-300 border border-transparent",
        "hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",
        "focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800",
        "active:bg-gray-200 dark:active:bg-gray-700"
      ),
      danger: cn(
        "bg-red-600 text-white border border-transparent",
        "hover:bg-red-700 hover:shadow-lg",
        "focus:ring-2 focus:ring-red-500 focus:ring-offset-2",
        "active:bg-red-800",
        "shadow-md hover:shadow-xl"
      )
    };

    // 过滤掉与Framer Motion冲突的属性
    const {
      onDrag,
      onDragStart,
      onDragEnd,
      onDragEnter,
      onDragLeave,
      onDragOver,
      onDrop,
      onAnimationStart,
      onAnimationEnd,
      onAnimationIteration,
      onTransitionEnd,
      ...filteredProps
    } = props as any;

    return (
      <motion.button
        ref={ref}
        className={cn(
          // 基础样式
          "inline-flex items-center justify-center font-semibold",
          "focus:outline-none transition-all duration-200 ease-out",
          "disabled:opacity-50 disabled:cursor-not-allowed",
          "relative overflow-hidden",
          
          // 尺寸
          sizeVariants[size],
          
          // 宽度
          fullWidth && "w-full",
          
          // 变体样式
          variantStyles[variant],
          
          // 禁用状态
          isDisabled && "hover:transform-none hover:shadow-none",
          
          className
        )}
        disabled={isDisabled}
        whileTap={!isDisabled ? { scale: 0.98 } : {}}
        whileHover={!isDisabled ? { scale: 1.02 } : {}}
        transition={{ duration: 0.15, ease: "easeOut" }}
        {...filteredProps}
      >
        {/* 渐变覆盖层 (仅在gradient为true时) */}
        {gradient && variant === 'primary' && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-purple-500 to-purple-600 opacity-0 hover:opacity-100 transition-opacity duration-200"
            initial={false}
          />
        )}

        {/* 内容容器 */}
        <div className="relative flex items-center justify-center gap-2">
          {/* 左侧图标或加载动画 */}
          {(icon && iconPosition === 'left') || loading ? (
            <span className="flex-shrink-0">
              {loading ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <Loader2 className="w-5 h-5" />
                </motion.div>
              ) : (
                icon
              )}
            </span>
          ) : null}

          {/* 文本内容 */}
          <span className={cn(
            "flex-1",
            fullWidth ? "text-center" : "",
            loading && "ml-2"
          )}>
            {children}
          </span>

          {/* 右侧图标 */}
          {icon && iconPosition === 'right' && !loading && (
            <span className="flex-shrink-0">
              {icon}
            </span>
          )}
        </div>

        {/* 涟漪效果 */}
        <motion.div
          className="absolute inset-0 bg-white opacity-0 rounded-inherit"
          whileTap={{ opacity: [0, 0.2, 0] }}
          transition={{ duration: 0.3 }}
        />
      </motion.button>
    );
  }
));

EnhancedAuthButton.displayName = "EnhancedAuthButton";

export { EnhancedAuthButton };
export type { EnhancedAuthButtonProps };
