import { test, expect } from '@playwright/test';

test.describe('Platform Icons Verification Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // 导航到测试页面
    await page.goto('/platform-integrations-test');
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    // 等待组件加载
    await page.waitForTimeout(3000);
  });

  test('should display Lazada icon specifically', async ({ page }) => {
    console.log('🔍 Testing Lazada icon...');
    
    // 检查是否有Lazada相关的图片元素
    const lazadaImages = page.locator('img[alt*="lazada"], img[src*="lazada"], img[title*="Lazada"]');
    
    // 等待图标加载
    await page.waitForTimeout(5000);
    
    // 检查Lazada图标数量
    const lazadaCount = await lazadaImages.count();
    console.log(`Found ${lazadaCount} Lazada images`);
    
    if (lazadaCount > 0) {
      // 检查第一个Lazada图标
      const firstLazada = lazadaImages.first();
      await expect(firstLazada).toBeVisible();
      
      // 获取图标属性
      const src = await firstLazada.getAttribute('src');
      const alt = await firstLazada.getAttribute('alt');
      console.log(`Lazada icon - src: ${src}, alt: ${alt}`);
      
      // 验证是否使用了正确的SVG链接
      if (src) {
        expect(src).toContain('lazada');
      }
    } else {
      console.log('❌ No Lazada icons found');
      // 截图用于调试
      await page.screenshot({ path: 'test-results/lazada-missing.png' });
    }
  });

  test('should display Webhook icon specifically', async ({ page }) => {
    console.log('🔍 Testing Webhook icon...');
    
    // 检查是否有Webhook相关的元素
    const webhookElements = page.locator('[title*="Webhook"], [alt*="webhook"], div:has-text("webhook")');
    
    // 等待图标加载
    await page.waitForTimeout(5000);
    
    // 检查Webhook图标数量
    const webhookCount = await webhookElements.count();
    console.log(`Found ${webhookCount} Webhook elements`);
    
    if (webhookCount > 0) {
      const firstWebhook = webhookElements.first();
      await expect(firstWebhook).toBeVisible();
      
      // 获取元素信息
      const title = await firstWebhook.getAttribute('title');
      console.log(`Webhook element - title: ${title}`);
    } else {
      console.log('❌ No Webhook icons found');
      // 截图用于调试
      await page.screenshot({ path: 'test-results/webhook-missing.png' });
    }
  });

  test('should display Messenger icon specifically', async ({ page }) => {
    console.log('🔍 Testing Messenger icon...');
    
    // 检查是否有Messenger相关的图片元素
    const messengerImages = page.locator('img[alt*="messenger"], img[src*="messenger"], img[title*="Messenger"]');
    
    // 等待图标加载
    await page.waitForTimeout(5000);
    
    // 检查Messenger图标数量
    const messengerCount = await messengerImages.count();
    console.log(`Found ${messengerCount} Messenger images`);
    
    if (messengerCount > 0) {
      const firstMessenger = messengerImages.first();
      await expect(firstMessenger).toBeVisible();
      
      // 获取图标属性
      const src = await firstMessenger.getAttribute('src');
      const alt = await firstMessenger.getAttribute('alt');
      console.log(`Messenger icon - src: ${src}, alt: ${alt}`);
    } else {
      console.log('❌ No Messenger icons found');
      // 截图用于调试
      await page.screenshot({ path: 'test-results/messenger-missing.png' });
    }
  });

  test('should display all 9 platform icons with detailed logging', async ({ page }) => {
    console.log('🔍 Testing all platform icons...');
    
    // 等待图标加载
    await page.waitForTimeout(5000);
    
    // 检查所有图片元素
    const allImages = page.locator('img');
    const imageCount = await allImages.count();
    console.log(`Total images found: ${imageCount}`);
    
    // 检查每个图片的详细信息
    for (let i = 0; i < imageCount; i++) {
      const img = allImages.nth(i);
      const src = await img.getAttribute('src');
      const alt = await img.getAttribute('alt');
      const title = await img.getAttribute('title');
      console.log(`Image ${i}: src=${src}, alt=${alt}, title=${title}`);
    }
    
    // 检查所有div元素（可能包含react-icons）
    const allDivs = page.locator('div[title]');
    const divCount = await allDivs.count();
    console.log(`Total titled divs found: ${divCount}`);
    
    for (let i = 0; i < divCount; i++) {
      const div = allDivs.nth(i);
      const title = await div.getAttribute('title');
      console.log(`Div ${i}: title=${title}`);
    }
    
    // 验证至少有一些图标显示
    expect(imageCount + divCount).toBeGreaterThan(0);
  });

  test('should check console logs for icon loading', async ({ page }) => {
    console.log('🔍 Checking console logs...');
    
    // 监听控制台消息
    const consoleMessages: string[] = [];
    page.on('console', msg => {
      consoleMessages.push(msg.text());
      console.log(`Console: ${msg.text()}`);
    });
    
    // 等待图标加载
    await page.waitForTimeout(5000);
    
    // 检查是否有相关的日志消息
    const iconLogs = consoleMessages.filter(msg => 
      msg.includes('icon') || 
      msg.includes('lazada') || 
      msg.includes('webhook') || 
      msg.includes('messenger')
    );
    
    console.log(`Found ${iconLogs.length} icon-related console messages:`);
    iconLogs.forEach(log => console.log(`  - ${log}`));
  });

});
