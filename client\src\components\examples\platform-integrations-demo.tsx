import { PlatformIntegrationsCloud } from "@/components/ui/platform-integrations-cloud"

// 我们集成的平台图标 slugs，包含 Lazada 和 webhook
const platformSlugs = [
  "gmail",
  "lazada", // Lazada 电商平台 (使用自定义SVG)
  "shopee", // Shopee 电商平台
  "tiktok", // TikTok 短视频平台
  "messenger", // Facebook Messenger (修正slug名称)
  "whatsapp",
  "instagram",
  "facebook",
  "webhook", // Webhook集成 (使用react-icons)
]

export function PlatformIntegrationsDemo() {
  return (
    <div className="relative flex size-full max-w-lg items-center justify-center overflow-hidden rounded-lg border bg-background px-20 pb-20 pt-8" data-testid="platform-integrations-cloud">
      <PlatformIntegrationsCloud iconSlugs={platformSlugs} />
    </div>
  )
}