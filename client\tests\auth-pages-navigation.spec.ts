import { test, expect } from '@playwright/test';

test.describe('Authentication Pages Navigation', () => {
  test.beforeEach(async ({ page }) => {
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
  });

  test('登录页面 - Back to Home按钮功能测试', async ({ page }) => {
    await page.goto('http://localhost:3003/login');
    
    // 检查Back to Home按钮是否存在
    const backButton = page.locator('a:has-text("Back to Home")');
    await expect(backButton).toBeVisible();
    
    // 检查按钮的href属性
    await expect(backButton).toHaveAttribute('href', '/home');
    
    // 点击按钮并验证导航
    await backButton.click();
    await page.waitForLoadState('networkidle');
    
    // 验证是否导航到home页面
    await expect(page).toHaveURL(/.*\/home/<USER>
  });

  test('注册页面 - Back to Home按钮功能测试', async ({ page }) => {
    await page.goto('http://localhost:3003/register');
    
    // 检查Back to Home按钮是否存在
    const backButton = page.locator('a:has-text("Back to Home")');
    await expect(backButton).toBeVisible();
    
    // 检查按钮的href属性
    await expect(backButton).toHaveAttribute('href', '/home');
    
    // 点击按钮并验证导航
    await backButton.click();
    await page.waitForLoadState('networkidle');
    
    // 验证是否导航到home页面
    await expect(page).toHaveURL(/.*\/home/<USER>
  });

  test('忘记密码页面 - Back to Home按钮功能测试', async ({ page }) => {
    await page.goto('http://localhost:3003/forgot-password');
    
    // 检查Back to Home按钮是否存在
    const backButton = page.locator('a:has-text("Back to Home")');
    await expect(backButton).toBeVisible();
    
    // 检查按钮的href属性
    await expect(backButton).toHaveAttribute('href', '/home');
    
    // 点击按钮并验证导航
    await backButton.click();
    await page.waitForLoadState('networkidle');
    
    // 验证是否导航到home页面
    await expect(page).toHaveURL(/.*\/home/<USER>
  });

  test('404页面 - Back to Home按钮功能测试', async ({ page }) => {
    await page.goto('http://localhost:3003/nonexistent-page');
    
    // 检查Back to Home按钮是否存在
    const backButton = page.locator('a:has-text("Back to Home")');
    await expect(backButton).toBeVisible();
    
    // 检查按钮的href属性
    await expect(backButton).toHaveAttribute('href', '/home');
    
    // 点击按钮并验证导航
    await backButton.click();
    await page.waitForLoadState('networkidle');
    
    // 验证是否导航到home页面
    await expect(page).toHaveURL(/.*\/home/<USER>
  });

  test('验证logo下面没有IteraBiz文字', async ({ page }) => {
    const pages = ['/login', '/register', '/forgot-password'];
    
    for (const pagePath of pages) {
      await page.goto(`http://localhost:3003${pagePath}`);
      
      // 检查logo图片存在
      const logoImg = page.locator('img[alt="IteraBiz Logo"]');
      await expect(logoImg).toBeVisible();
      
      // 检查logo下面没有IteraBiz文字（在logo链接内部）
      const logoLink = page.locator('a[href="/home"]');
      const iteraBizText = logoLink.locator('span:has-text("IteraBiz")');
      await expect(iteraBizText).not.toBeVisible();
    }
  });

  test('验证输入框占位符已优化', async ({ page }) => {
    // 测试登录页面
    await page.goto('http://localhost:3003/login');
    
    const emailInput = page.locator('input[type="email"]');
    await expect(emailInput).toHaveAttribute('placeholder', '<EMAIL>');
    
    const passwordInput = page.locator('input[type="password"]');
    await expect(passwordInput).toHaveAttribute('placeholder', '••••••••');
    
    // 测试注册页面第一步
    await page.goto('http://localhost:3003/register');
    
    const firstNameInput = page.locator('input[placeholder="First name"]');
    await expect(firstNameInput).toBeVisible();
    
    const lastNameInput = page.locator('input[placeholder="Last name"]');
    await expect(lastNameInput).toBeVisible();
    
    const emailInputRegister = page.locator('input[placeholder="<EMAIL>"]');
    await expect(emailInputRegister).toBeVisible();
    
    const companyInput = page.locator('input[placeholder="Company name (optional)"]');
    await expect(companyInput).toBeVisible();
    
    // 测试忘记密码页面
    await page.goto('http://localhost:3003/forgot-password');
    
    const forgotEmailInput = page.locator('input[placeholder="<EMAIL>"]');
    await expect(forgotEmailInput).toBeVisible();
  });

  test('验证注册页面第二步密码占位符', async ({ page }) => {
    await page.goto('http://localhost:3003/register');
    
    // 填写第一步表单
    await page.fill('input[placeholder="First name"]', 'Test');
    await page.fill('input[placeholder="Last name"]', 'User');
    await page.fill('input[placeholder="<EMAIL>"]', '<EMAIL>');
    
    // 点击Continue按钮进入第二步
    await page.click('button:has-text("Continue")');
    await page.waitForTimeout(500);
    
    // 检查密码字段占位符
    const passwordInputs = page.locator('input[placeholder="••••••••"]');
    await expect(passwordInputs).toHaveCount(2); // 密码和确认密码
  });
});
