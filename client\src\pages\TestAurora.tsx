import React from 'react';
import { AuroraBackground } from '../components/ui/aurora-background';

export default function TestAurora() {
  return (
    <AuroraBackground className="min-h-screen flex items-center justify-center">
      <div className="text-center space-y-6 z-10">
        <h1 className="text-6xl font-bold text-gray-900 dark:text-white">
          Aurora 特效测试
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300">
          如果您能看到背景中的动态渐变效果，说明Aurora特效正常工作
        </p>
        <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-lg p-6 border border-white/30 dark:border-gray-700/50">
          <p className="text-lg">
            🌟 Aurora背景特效包含：
          </p>
          <ul className="mt-4 space-y-2 text-left">
            <li>✨ 动态渐变动画</li>
            <li>🎨 蓝色到紫色的颜色过渡</li>
            <li>🌈 60秒循环动画</li>
            <li>🌗 明暗主题适配</li>
          </ul>
        </div>
      </div>
    </AuroraBackground>
  );
} 