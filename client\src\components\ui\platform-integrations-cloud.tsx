"use client"

import React, { useState, useEffect, useMemo } from "react"
import { useTheme } from "next-themes"
import {
  Cloud,
  fetchSimpleIcons,
  ICloud,
  renderSimpleIcon,
} from "react-icon-cloud"
import { useTranslation } from "react-i18next"

// --- SVG Data URIs for local icons ---
const lazadaIconDataUri = "data:image/svg+xml,%3Csvg%20version='1.0'%20id='Layer_1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20x='0px'%20y='0px'%20viewBox='0%200%20624.3%20624.3'%20enable-background='new%200%200%20624.3%20624.3'%20xml:space='preserve'%3E%3Cg%3E%3ClinearGradient%20id='SVGID_1_'%20gradientUnits='userSpaceOnUse'%20x1='305.8847'%20y1='-453.5474'%20x2='313.9513'%20y2='635.428'%20gradientTransform='matrix(1%200%200%201%200%20-80.11)'%3E%3Cstop%20offset='0.3379'%20style='stop-color:%235B8BEB'/%3E%3Cstop%20offset='0.7794'%20style='stop-color:%232742A2'/%3E%3Cstop%20offset='1'%20style='stop-color:%23112283'/%3E%3C/linearGradient%3E%3Crect%20fill='url(%23SVGID_1_)'%20width='624.3'%20height='624.3'/%3E%3Cg%20id='g942'%20transform='matrix(1.3719271,0,0,1.3719271,169.4857,-38.493576)'%3E%3Cg%20id='g71'%20transform='matrix(3.3097462,0,0,3.3097462,-688.02908,65.088537)'%3E%3ClinearGradient%20id='path69_1_'%20gradientUnits='userSpaceOnUse'%20x1='189.5344'%20y1='170.1424'%20x2='279.1699'%20y2='170.8368'%20gradientTransform='matrix(114.8374%200%200%20-114.8374%20-21581.2324%2019599.6074)'%3E%3Cstop%20offset='9.999999e-08'%20style='stop-color:%23FFB900'/%3E%3Cstop%20offset='0.3376'%20style='stop-color:%23F38000'/%3E%3Cstop%20offset='0.5673'%20style='stop-color:%23F83C72'/%3E%3Cstop%20offset='0.78'%20style='stop-color:%23FC1CBE'/%3E%3Cstop%20offset='0.93'%20style='stop-color:%23FE08ED'/%3E%3Cstop%20offset='1'%20style='stop-color:%23FF00FF'/%3E%3C/linearGradient%3E%3Cpath%20id='path69'%20fill='url(%23path69_1_)'%20d='M239.6,105.7c-0.8,0-1.6-0.2-2.2-0.6c-5.9-3.4-49.4-30.7-51-31.5%20c-1.2-0.6-2.1-1.8-2.2-3.2V32.2c0-1.4,0.7-2.8,1.9-3.6l0.3-0.2c4.2-2.6,18.3-11.2,20.5-12.4c0.5-0.3,1.1-0.5,1.7-0.5%20c0.6,0,1.1,0.2,1.6,0.4c0,0,19.7,12.8,22.7,14c2.1,1,4.4,1.5,6.8,1.4c2.6,0.1,5.2-0.6,7.6-1.8c3-1.6,21.8-13.5,22-13.5%20c0.5-0.3,1-0.4,1.6-0.4s1.2,0.2,1.7,0.5c2.6,1.4,20,12.1,20.8,12.6c1.2,0.7,2,2.1,2,3.5v38.2c-0.1,1.4-1,2.6-2.2,3.2%20c-1.6,0.9-45,28.2-51,31.5C241.2,105.5,240.4,105.7,239.6,105.7'/%3E%3C/g%3E%3Cg%20id='g79'%20transform='matrix(3.3097462,0,0,3.3097462,-688.02908,65.088537)'%3E%3ClinearGradient%20id='path77_1_'%20gradientUnits='userSpaceOnUse'%20x1='189.8032'%20y1='169.829'%20x2='281.1905'%20y2='220.4294'%20gradientTransform='matrix(114.8374%200%200%20-114.8374%20-21581.2324%2019599.6074)'%3E%3Cstop%20offset='0'%20style='stop-color:%23EE0A3F'/%3E%3Cstop%20offset='1'%20style='stop-color:%23EE0A3F;stop-opacity:0'/%3E%3C/linearGradient%3E%3Cpath%20id='path77'%20fill='url(%23path77_1_)'%20d='M239.4,105.7h0.2c0.8,0,1.6-0.2,2.2-0.6c5.9-3.4,49.3-30.7,51-31.5%20c1.3-0.6,2.1-1.8,2.2-3.2V32.2c0-0.7-0.1-1.3-0.4-1.9l-55.3,30.4L239.4,105.7'/%3E%3C/g%3E%3Cg%20id='g970'%20transform='matrix(-3.2744849,0,0,3.2744849,-467.18522,65.934126)'%3E%3ClinearGradient%20id='path968_1_'%20gradientUnits='userSpaceOnUse'%20x1='-47.8267'%20y1='171.4726'%20x2='-3.1901'%20y2='207.4298'%20gradientTransform='matrix(-111.2069%200%200%20-111.2069%20-5467.0049%2019137.0176)'%3E%3Cstop%20offset='0'%20style='stop-color:%23ED6600'/%3E%3Cstop%20offset='1'%20style='stop-color:%23F98200'/%3E%3C/linearGradient%3E%3Cpath%20id='path968'%20fill='url(%23path968_1_)'%20d='M-174.4,106h0.2c0.8,0,1.6-0.2,2.2-0.6c5.9-3.4,49.3-30.7,51-31.5%20c1.3-0.6,2.1-1.8,2.2-3.2V32.6c0-0.7-0.1-1.3-0.4-1.9l-55.3,30.4V106'/%3E%3C/g%3E%3C/g%3E%3Cg%3E%3Cg%20id='g35_1_'%20transform='matrix(12.509277,0,0,12.509277,438.06846,8.29825)'%3E%3Cpath%20id='path33_1_'%20fill='%23FFFFFF'%20d='M-19.7,19.6h1.8v8.8h3.2v1.7h-5V19.6'/%3E%3C/g%3E%3Cg%20id='g41_1_'%20transform='matrix(12.509277,0,0,12.509277,438.06846,8.29825)'%3E%3Cpath%20id='path39_1_'%20fill='%23FFFFFF'%20d='M-10.5,22.3c1,0,1.9,0.4,2.5,1.2v-1h1.7v7.8H-8v-1c-0.6,0.8-1.5,1.2-2.5,1.2%20c-2.1,0-3.7-1.8-3.7-4C-14.2,24.1-12.6,22.3-10.5,22.3%20M-10.2,23.8c-1.4,0-2.3,1.1-2.3,2.5s0.9,2.5,2.3,2.5s2.3-1.1,2.3-2.5%20S-8.8,23.8-10.2,23.8'/%3E%3C/g%3E%3Cg%20id='g47_1_'%20transform='matrix(12.509277,0,0,12.509277,438.06846,8.29825)'%3E%3Cpath%20id='path45_1_'%20fill='%23FFFFFF'%20d='M-5,28.9l4.1-4.9h-4v-1.5h6.2v1.4l-4,4.8h4.1v1.5H-5V28.9'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E";
const webhookIconDataUri = "data:image/svg+xml,%3Csvg viewBox='-10 -5 1034 1034' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23_COLOR_' d='M482 226h-1l-10 2q-33 4 -64.5 18.5t-55.5 38.5q-41 37 -57 91q-9 30 -8 63t12 63q17 45 52 78l13 12l-83 135q-26 -1 -45 7q-30 13 -45 40q-7 15 -9 31t2 32q8 30 33 48q15 10 33 14.5t36 2t34.5 -12.5t27.5 -25q12 -17 14.5 -39t-5.5 -41q-1 -5 -7 -14l-3 -6l118 -192q6 -9 8 -14l-10 -3q-9 -2 -13 -4q-23 -10 -41.5 -27.5t-28.5 -39.5q-17 -36 -9 -75q4 -23 17 -43t31 -34q37 -27 82 -27q27 -1 52.5 9.5t44.5 30.5q17 16 26.5 38.5t10.5 45.5q0 17 -6 42l70 19l8 1q14 -43 7 -86q-4 -33 -19.5 -63.5t-39.5 -53.5q-42 -42 -103 -56q-6 -2 -18 -4l-14 -2h-37zM500 350q-17 0 -34 7t-30.5 20.5t-19.5 31.5q-8 20 -4 44q3 18 14 34t28 25q24 15 56 13q3 4 5 8l112 191q3 6 6 9q27 -26 58.5 -35.5t65 -3.5t58.5 26q32 25 43.5 61.5t0.5 73.5q-8 28 -28.5 50t-48.5 33q-31 13 -66.5 8.5t-63.5 -24.5q-4 -3 -13 -10l-5 -6q-4 3 -11 10l-47 46q23 23 52 38.5t61 21.5l22 4h39l28 -5q64 -13 110 -60q22 -22 36.5 -50.5t19.5 -59.5q5 -36 -2 -71.5t-25 -64.5t-44 -51t-57 -35q-34 -14 -70.5 -16t-71.5 7l-17 5l-81 -137q13 -19 16 -37q5 -32 -13 -60q-16 -25 -44 -35q-17 -6 -35 -6zM218 614q-58 13 -100 53q-47 44 -61 105l-4 24v37l2 11q2 13 4 20q7 31 24.5 59t42.5 49q50 41 115 49q38 4 76 -4.5t70 -28.5q53 -34 78 -91q7 -17 14 -45q6 -1 18 0l125 2q14 0 20 1q11 20 25 31t31.5 16t35.5 4q28 -3 50 -20q27 -21 32 -54q2 -17 -1.5 -33t-13.5 -30q-16 -22 -41 -32q-17 -7 -35.5 -6.5t-35.5 7.5q-28 12 -43 37l-3 6q-14 0 -42 -1l-113 -1q-15 -1 -43 -1l-50 -1l3 17q8 43 -13 81q-14 27 -40 45t-57 22q-35 6 -70 -7.5t-57 -42.5q-28 -35 -27 -79q1 -37 23 -69q13 -19 32 -32t41 -19l9 -3z' /%3E%3C/svg%3E";

export const cloudProps: Omit<ICloud, "children"> = {
  containerProps: {
    style: {
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      width: "100%",
      paddingTop: 40,
    },
  },
  options: {
    reverse: true,
    depth: 1,
    wheelZoom: false,
    imageScale: 2,
    activeCursor: "default",
    tooltip: "native",
    initial: [0.1, -0.1],
    clickToFront: 500,
    tooltipDelay: 0,
    outlineColour: "#0000",
    maxSpeed: 0.04,
    minSpeed: 0.02,
    // dragControl: false,
  },
}

export const renderCustomIcon = (icon: any, theme: string) => {
  return renderSimpleIcon({
    icon,
    bgHex: theme === "light" ? "#f3f2ef" : "#080510",
    fallbackHex: theme === "light" ? "#6e6e73" : "#ffffff",
    minContrastRatio: theme === "dark" ? 2 : 1.2,
    size: 42,
    aProps: {
      href: undefined,
      target: undefined,
      rel: undefined,
      onClick: (e: any) => e.preventDefault(),
    },
  })
}

export type DynamicCloudProps = {
  iconSlugs?: string[]
}

type IconData = Awaited<ReturnType<typeof fetchSimpleIcons>>

// 定义平台图标类型
type PlatformIcon =
  | { slug: string; type: "simple" }
  | { slug: string; type: "img"; src: string }

// ------------- Platform Icons Definition -------------
const platformIcons: PlatformIcon[] = [
  { slug: "gmail", type: "simple" },
  { slug: "lazada", type: "img", src: lazadaIconDataUri },
  { slug: "shopee", type: "simple" },
  { slug: "tiktok", type: "simple" },
  { slug: "messenger", type: "simple" },
  { slug: "whatsapp", type: "simple" },
  { slug: "instagram", type: "simple" },
  { slug: "facebook", type: "simple" },
  { slug: "webhook", type: "img", src: webhookIconDataUri },
]

// 提取需要 simple-icons 的 slug 数组
const simpleIconSlugs = platformIcons
  .filter((i) => i.type === "simple")
  .map((i) => i.slug)
  
const allPlatformSlugs = platformIcons.map((i) => i.slug)

export function PlatformIntegrationsCloud({ iconSlugs = allPlatformSlugs }: DynamicCloudProps) {
  const [data, setData] = useState<IconData | null>(null)
  const { theme } = useTheme()

  useEffect(() => {
    fetchSimpleIcons({ slugs: simpleIconSlugs }).then(setData)
  }, [])

  const filteredIcons = useMemo(() => {
    return iconSlugs.map(slug => platformIcons.find(i => i.slug === slug)).filter(Boolean) as PlatformIcon[]
  }, [iconSlugs])

  const renderedIcons = useMemo(() => {
    const simpleIconsReady = !!data
    if (!simpleIconsReady) {
      return []
    }
    return filteredIcons.map((icon) => {
      if (icon.type === "img") {
        let src = icon.src;
        if (icon.slug === 'webhook') {
          const color = theme === 'dark' ? 'ffffff' : '000000';
          src = src.replace('_COLOR_', color);
        }
        return (
          <a key={icon.slug} href="#" onClick={(e) => e.preventDefault()} title={icon.slug.charAt(0).toUpperCase() + icon.slug.slice(1)}>
            <img
              width={42}
              height={42}
              src={src}
              alt={icon.slug}
              style={{ borderRadius: '8px' }}
            />
          </a>
        );
      }
      
      if (data?.simpleIcons[icon.slug]) {
        const iconData = { ...data.simpleIcons[icon.slug] };
        if (theme === 'dark' && icon.slug === 'tiktok') {
          iconData.hex = 'FFFFFF';
        }
        return renderCustomIcon(iconData as any, theme || "light")
      } 
      
      // Fallback for missing icons
      return (
        <a key={icon.slug} href="#" onClick={(e) => e.preventDefault()} title={icon.slug.charAt(0).toUpperCase() + icon.slug.slice(1)}>
          <div
            style={{
              width: 42,
              height: 42,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: theme === "dark" ? "#374151" : "#f3f4f6",
              borderRadius: 8,
              color: theme === "dark" ? "#ffffff" : "#374151",
              border: '2px solid #e5e7eb',
              fontSize: '12px',
              textAlign: 'center'
            }}
          >
            {icon.slug.substring(0, 2).toUpperCase()}
          </div>
        </a>
      )
    })
  }, [data, theme, filteredIcons])

  return (
    <div style={{ position: 'relative' }}>
      {/* 添加强制显示样式 - 更强力的CSS覆盖 */}
      <style>{`
        .platform-icon-cloud * {
          visibility: visible !important;
          opacity: 1 !important;
        }
        .platform-icon-cloud img {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          position: relative !important;
          z-index: 1000 !important;
        }
        .platform-icon-cloud div[title] {
          display: flex !important;
          visibility: visible !important;
          opacity: 1 !important;
          position: relative !important;
          z-index: 1000 !important;
        }
        .platform-icon-cloud canvas {
          position: relative !important;
          z-index: 1 !important;
        }
        /* 强制显示所有子元素 */
        .platform-icon-cloud > * {
          visibility: visible !important;
          opacity: 1 !important;
        }
      `}</style>
      {/* @ts-ignore */}
      <Cloud {...cloudProps} className="platform-icon-cloud">
        <>{renderedIcons}</>
      </Cloud>
    </div>
  )
}

// 更新Demo组件使用 platformIcons 自动构建图标
export function PlatformIntegrationsCloudDemo() {
  return (
    <div className="relative flex size-full max-w-lg items-center justify-center overflow-hidden rounded-lg border bg-background px-20 pb-20 pt-8 " data-testid="platform-integrations-cloud">
      <PlatformIntegrationsCloud />
    </div>
  )
}

// 集成展示section组件 - 供HomePage使用
export function IntegrationsSection() {
  const { t } = useTranslation();
  return (
    <section id="features" className="py-20 px-4 bg-gradient-to-b from-background to-muted/20">
      <div className="container mx-auto max-w-4xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            {t("homePage.integrations.title")}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {t("homePage.integrations.description")}
          </p>
        </div>

        {/* 居中显示图标云 */}
        <div className="flex justify-center">
          <div className="relative flex w-full max-w-lg items-center justify-center overflow-hidden rounded-lg bg-background px-8 pb-8 pt-8">
            <div className="w-full h-96 flex items-center justify-center">
              <PlatformIntegrationsCloud />
            </div>
          </div>
        </div>

        {/* 底部描述 */}
        <div className="text-center mt-12">
          <h3 className="text-xl font-semibold text-foreground mb-3">
            {t("homePage.integrations.footer.title")}
          </h3>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            {t("homePage.integrations.footer.description")}
          </p>
        </div>
      </div>
    </section>
  )
}


