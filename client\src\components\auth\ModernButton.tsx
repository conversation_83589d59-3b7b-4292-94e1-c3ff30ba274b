import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

interface ModernButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export function ModernButton({
  className,
  variant = 'primary',
  size = 'md',
  loading = false,
  leftIcon,
  rightIcon,
  children,
  disabled,
  ...props
}: ModernButtonProps) {
  const baseClasses = "relative inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed";
  
  const variantClasses = {
    primary: [
      "bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600",
      "hover:from-purple-700 hover:via-blue-700 hover:to-indigo-700",
      "text-white shadow-lg hover:shadow-xl",
      "focus:ring-purple-500/50",
      "before:absolute before:inset-0 before:rounded-lg before:bg-gradient-to-r before:from-purple-600/20 before:via-blue-600/20 before:to-indigo-600/20 before:opacity-0 hover:before:opacity-100 before:transition-opacity"
    ],
    secondary: [
      "bg-gray-100 dark:bg-gray-800",
      "hover:bg-gray-200 dark:hover:bg-gray-700",
      "text-gray-900 dark:text-white",
      "focus:ring-gray-500/50"
    ],
    outline: [
      "border-2 border-purple-500 dark:border-purple-400",
      "hover:bg-purple-50 dark:hover:bg-purple-950/20",
      "text-purple-600 dark:text-purple-400",
      "focus:ring-purple-500/50"
    ],
    ghost: [
      "hover:bg-gray-100 dark:hover:bg-gray-800",
      "text-gray-600 dark:text-gray-300",
      "focus:ring-gray-500/50"
    ]
  };

  const sizeClasses = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg"
  };

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
      )}
      
      {!loading && leftIcon && (
        <span className="mr-2">{leftIcon}</span>
      )}
      
      <span className="relative z-10">{children}</span>
      
      {!loading && rightIcon && (
        <span className="ml-2">{rightIcon}</span>
      )}
    </button>
  );
} 