import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage?: number;
  componentCount: number;
}

interface PerformanceMonitorProps {
  enabled?: boolean;
  showMetrics?: boolean;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}

export function PerformanceMonitor({ 
  enabled = process.env.NODE_ENV === 'development',
  showMetrics = false,
  onMetricsUpdate 
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!enabled) return;

    const startTime = performance.now();
    let renderStartTime = performance.now();

    // 监控页面加载性能
    const measurePerformance = () => {
      const loadTime = performance.now() - startTime;
      const renderTime = performance.now() - renderStartTime;
      
      // 获取内存使用情况 (如果支持)
      const memoryUsage = (performance as any).memory?.usedJSHeapSize;
      
      // 计算组件数量 (简单估算)
      const componentCount = document.querySelectorAll('[data-react-component]').length || 
                           document.querySelectorAll('div, span, button, input').length;

      const newMetrics: PerformanceMetrics = {
        loadTime: Math.round(loadTime),
        renderTime: Math.round(renderTime),
        memoryUsage: memoryUsage ? Math.round(memoryUsage / 1024 / 1024) : undefined,
        componentCount
      };

      setMetrics(newMetrics);
      onMetricsUpdate?.(newMetrics);
    };

    // 延迟测量以确保组件完全渲染
    const timer = setTimeout(measurePerformance, 100);

    // 监听性能条目
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            renderStartTime = entry.startTime;
          }
        });
      });

      try {
        observer.observe({ entryTypes: ['navigation', 'measure'] });
      } catch (e) {
        console.warn('Performance Observer not fully supported');
      }

      return () => {
        clearTimeout(timer);
        observer.disconnect();
      };
    }

    return () => clearTimeout(timer);
  }, [enabled, onMetricsUpdate]);

  // 键盘快捷键切换显示
  useEffect(() => {
    if (!enabled) return;

    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        setIsVisible(!isVisible);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [enabled, isVisible]);

  if (!enabled || (!showMetrics && !isVisible) || !metrics) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
        className="fixed bottom-4 right-4 z-50 bg-black/80 text-white text-xs rounded-lg p-3 font-mono backdrop-blur-sm"
        style={{ minWidth: '200px' }}
      >
        <div className="flex items-center justify-between mb-2">
          <span className="font-semibold">Performance</span>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-white transition-colors"
          >
            ×
          </button>
        </div>
        
        <div className="space-y-1">
          <div className="flex justify-between">
            <span>Load Time:</span>
            <span className={metrics.loadTime > 1000 ? 'text-red-400' : 'text-green-400'}>
              {metrics.loadTime}ms
            </span>
          </div>
          
          <div className="flex justify-between">
            <span>Render Time:</span>
            <span className={metrics.renderTime > 500 ? 'text-yellow-400' : 'text-green-400'}>
              {metrics.renderTime}ms
            </span>
          </div>
          
          {metrics.memoryUsage && (
            <div className="flex justify-between">
              <span>Memory:</span>
              <span className={metrics.memoryUsage > 50 ? 'text-yellow-400' : 'text-green-400'}>
                {metrics.memoryUsage}MB
              </span>
            </div>
          )}
          
          <div className="flex justify-between">
            <span>Elements:</span>
            <span className="text-blue-400">{metrics.componentCount}</span>
          </div>
        </div>
        
        <div className="mt-2 pt-2 border-t border-gray-600 text-gray-400">
          Press Ctrl+Shift+P to toggle
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

// 性能分析Hook
export function usePerformanceAnalysis() {
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);

  const addMetrics = (newMetrics: PerformanceMetrics) => {
    setMetrics(prev => [...prev.slice(-9), newMetrics]); // 保留最近10条记录
  };

  const getAverageMetrics = (): Partial<PerformanceMetrics> => {
    if (metrics.length === 0) return {};

    const avg = metrics.reduce(
      (acc, metric) => ({
        loadTime: acc.loadTime + metric.loadTime,
        renderTime: acc.renderTime + metric.renderTime,
        componentCount: acc.componentCount + metric.componentCount
      }),
      { loadTime: 0, renderTime: 0, componentCount: 0 }
    );

    return {
      loadTime: Math.round(avg.loadTime / metrics.length),
      renderTime: Math.round(avg.renderTime / metrics.length),
      componentCount: Math.round(avg.componentCount / metrics.length)
    };
  };

  const getPerformanceScore = (): number => {
    const avg = getAverageMetrics();
    if (!avg.loadTime || !avg.renderTime) return 0;

    // 简单的性能评分算法 (0-100)
    const loadScore = Math.max(0, 100 - (avg.loadTime / 10));
    const renderScore = Math.max(0, 100 - (avg.renderTime / 5));
    
    return Math.round((loadScore + renderScore) / 2);
  };

  return {
    metrics,
    addMetrics,
    getAverageMetrics,
    getPerformanceScore,
    clearMetrics: () => setMetrics([])
  };
}
