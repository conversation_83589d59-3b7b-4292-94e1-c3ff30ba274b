import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { EyeOff, Eye, AlertCircle, CheckCircle, Mail, Lock, User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { authTokensV2 } from '@/design-system/tokens/auth-tokens';

interface EnhancedAuthInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 
  'onDrag' | 'onDragStart' | 'onDragEnd' | 'onDragEnter' | 'onDragLeave' | 'onDragOver' | 'onDrop' |
  'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration' | 'onTransitionEnd'
> {
  label?: string;
  error?: string;
  success?: string;
  hint?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  showPasswordToggle?: boolean;
  variant?: 'default' | 'floating' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  autoIcon?: boolean; // 自动根据type显示图标
}

// 自动图标映射
const getAutoIcon = (type: string) => {
  switch (type) {
    case 'email':
      return <Mail className="w-5 h-5" />;
    case 'password':
      return <Lock className="w-5 h-5" />;
    case 'text':
      return <User className="w-5 h-5" />;
    default:
      return null;
  }
};

const EnhancedAuthInput = React.memo(React.forwardRef<HTMLInputElement, EnhancedAuthInputProps>(
  ({ 
    label,
    error,
    success,
    hint,
    leftIcon,
    rightIcon,
    showPasswordToggle = false,
    variant = 'default',
    size = 'md',
    autoIcon = false,
    type = 'text',
    className,
    ...props 
  }, ref) => {
    const [showPassword, setShowPassword] = useState(false);
    const [isFocused, setIsFocused] = useState(false);
    const [hasValue, setHasValue] = useState(!!props.value || !!props.defaultValue);

    const inputType = showPasswordToggle ? (showPassword ? 'text' : 'password') : type;
    const hasError = !!error;
    const hasSuccess = !!success;
    const isFloating = variant === 'floating';

    // 自动图标
    const autoIconElement = autoIcon ? getAutoIcon(type) : null;
    const finalLeftIcon = leftIcon || autoIconElement;

    // 尺寸变体 (响应式) - 分离左右内边距以避免与图标样式冲突
    const sizeVariants = {
      sm: finalLeftIcon
        ? "py-2 pr-3 text-sm sm:py-2.5 sm:pr-3.5"
        : "py-2 px-3 text-sm sm:py-2.5 sm:px-3.5",
      md: finalLeftIcon
        ? "py-3 pr-4 text-base sm:py-3.5 sm:pr-4"
        : "py-3 px-4 text-base sm:py-3.5 sm:px-4",
      lg: finalLeftIcon
        ? "py-4 pr-5 text-lg sm:py-4.5 sm:pr-5.5"
        : "py-4 px-5 text-lg sm:py-4 sm:px-6"
    };

    // 处理输入变化
    const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(e.target.value.length > 0);
      props.onChange?.(e);
    }, [props.onChange]);

    // 过滤掉与Framer Motion冲突的属性
    const {
      onDrag,
      onDragStart,
      onDragEnd,
      onDragEnter,
      onDragLeave,
      onDragOver,
      onDrop,
      onAnimationStart,
      onAnimationEnd,
      onAnimationIteration,
      onTransitionEnd,
      onChange,
      ...filteredProps
    } = props as any;

    return (
      <div className="space-y-2">
        {/* 标签 (非浮动模式) */}
        {label && !isFloating && (
          <motion.label 
            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            {label}
          </motion.label>
        )}

        {/* 输入框容器 */}
        <div className="relative">
          {/* 左侧图标 */}
          {finalLeftIcon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none z-10">
              {finalLeftIcon}
            </div>
          )}

          {/* 输入框 */}
          <motion.input
            ref={ref}
            type={inputType}
            className={cn(
              // 基础样式
              'w-full border rounded-xl transition-all duration-200 ease-out',
              'placeholder:text-gray-400 dark:placeholder:text-gray-500 text-gray-900 dark:text-white',
              'focus:outline-none focus:ring-0',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              'bg-white dark:bg-gray-800',
              
              // 尺寸
              sizeVariants[size],
              
              // 左侧图标间距 - 根据尺寸调整
              finalLeftIcon && (size === 'sm' ? 'pl-10' : size === 'lg' ? 'pl-12' : 'pl-11'),
              
              // 右侧图标间距
              (rightIcon || showPasswordToggle || hasError || hasSuccess) && 'pr-11',
              
              // 变体样式
              variant === 'minimal' && 'border-0 border-b-2 rounded-none bg-transparent',
              variant === 'floating' && 'pt-6',
              
              // 默认状态
              !hasError && !hasSuccess && !isFocused && [
                'border-gray-200 dark:border-gray-600',
                'hover:border-purple-300 dark:hover:border-purple-500'
              ],
              
              // 聚焦状态
              !hasError && !hasSuccess && isFocused && [
                'border-purple-500 dark:border-purple-400',
                'shadow-lg shadow-purple-100 dark:shadow-purple-900/20'
              ],
              
              // 错误状态
              hasError && [
                'border-red-300 bg-red-50',
                'focus:border-red-500 focus:shadow-lg focus:shadow-red-100',
                'dark:border-red-500 dark:bg-red-900/20'
              ],
              
              // 成功状态
              hasSuccess && [
                'border-green-300 bg-green-50',
                'focus:border-green-500 focus:shadow-lg focus:shadow-green-100',
                'dark:border-green-500 dark:bg-green-900/20'
              ],
              
              className
            )}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            onChange={handleInputChange}
            whileFocus={{ scale: 1.01 }}
            transition={{ duration: 0.15, ease: "easeOut" }}
            {...filteredProps}
          />

          {/* 浮动标签 */}
          {label && isFloating && (
            <motion.label
              className={cn(
                "absolute left-4 transition-all duration-200 pointer-events-none",
                "text-gray-500 dark:text-gray-400",
                (isFocused || hasValue) ? [
                  "top-2 text-xs font-medium",
                  isFocused && "text-purple-600 dark:text-purple-400"
                ] : "top-1/2 transform -translate-y-1/2 text-base",
                finalLeftIcon && "left-11"
              )}
              animate={{
                y: (isFocused || hasValue) ? 0 : 0,
                scale: (isFocused || hasValue) ? 0.85 : 1,
              }}
              transition={{ duration: 0.2, ease: "easeOut" }}
            >
              {label}
            </motion.label>
          )}

          {/* 右侧图标区域 */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
            {/* 状态图标 */}
            <AnimatePresence mode="wait">
              {hasError && (
                <motion.div
                  key="error"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  <AlertCircle className="w-5 h-5 text-red-500" />
                </motion.div>
              )}
              {hasSuccess && !hasError && (
                <motion.div
                  key="success"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  <CheckCircle className="w-5 h-5 text-green-500" />
                </motion.div>
              )}
            </AnimatePresence>

            {/* 密码切换按钮 */}
            {showPasswordToggle && (
              <motion.button
                type="button"
                className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                onClick={() => setShowPassword(!showPassword)}
                whileTap={{ scale: 0.95 }}
                transition={{ duration: 0.1 }}
              >
                {showPassword ? (
                  <EyeOff className="w-5 h-5" />
                ) : (
                  <Eye className="w-5 h-5" />
                )}
              </motion.button>
            )}

            {/* 自定义右侧图标 */}
            {rightIcon && !showPasswordToggle && (
              <div className="text-gray-400">
                {rightIcon}
              </div>
            )}
          </div>
        </div>

        {/* 提示信息 */}
        <AnimatePresence mode="wait">
          {(error || success || hint) && (
            <motion.div
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              transition={{ duration: 0.2 }}
              className="space-y-1"
            >
              {error && (
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center gap-1">
                  <AlertCircle className="w-4 h-4" />
                  {error}
                </p>
              )}
              {success && !error && (
                <p className="text-sm text-green-600 dark:text-green-400 flex items-center gap-1">
                  <CheckCircle className="w-4 h-4" />
                  {success}
                </p>
              )}
              {hint && !error && !success && (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {hint}
                </p>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }
));

EnhancedAuthInput.displayName = "EnhancedAuthInput";

export { EnhancedAuthInput };
export type { EnhancedAuthInputProps };
