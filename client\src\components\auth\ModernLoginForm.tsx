import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import { ModernInput } from './ModernInput';
import { ModernButton } from './ModernButton';
import { Mail, Lock, Chrome } from 'lucide-react';
import { motion } from 'framer-motion';

export function ModernLoginForm() {
  const { login, signInWithGoogle } = useAuth();
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};
    
    if (!formData.email) {
      newErrors.email = '请输入邮箱地址';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }
    
    if (!formData.password) {
      newErrors.password = '请输入密码';
    } else if (formData.password.length < 6) {
      newErrors.password = '密码至少需要6位字符';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setLoading(true);
    setErrors({});
    
    try {
      const { success, error } = await login(formData.email, formData.password);
      
      if (!success) {
        toast.error(error || '登录失败，请检查您的凭据');
        setErrors({ general: error || '登录失败' });
      } else {
        toast.success('登录成功！');
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('登录过程中发生错误');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setGoogleLoading(true);
    try {
      await signInWithGoogle();
    } catch (error) {
      console.error('Google login error:', error);
      toast.error('Google登录失败');
    } finally {
      setGoogleLoading(false);
    }
  };

  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* 通用错误信息 */}
      {errors.general && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <p className="text-sm text-red-600 dark:text-red-400">{errors.general}</p>
        </motion.div>
      )}

      {/* 登录表单 */}
      <form onSubmit={handleSubmit} className="space-y-5">
        <ModernInput
          label="邮箱地址"
          type="email"
          value={formData.email}
          onChange={handleInputChange('email')}
          error={errors.email}
          leftIcon={<Mail className="w-5 h-5" />}
          autoComplete="email"
        />

        <ModernInput
          label="密码"
          type="password"
          value={formData.password}
          onChange={handleInputChange('password')}
          error={errors.password}
          leftIcon={<Lock className="w-5 h-5" />}
          showPasswordToggle
          autoComplete="current-password"
        />

        <div className="flex items-center justify-between">
          <label className="flex items-center">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-purple-600 focus:ring-purple-500 focus:ring-offset-0"
            />
            <span className="ml-2 text-sm text-gray-600 dark:text-gray-300">记住我</span>
          </label>
          
          <Link
            to="/forgot-password"
            className="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-500 dark:hover:text-purple-300 font-medium transition-colors"
          >
            忘记密码？
          </Link>
        </div>

        <ModernButton
          type="submit"
          loading={loading}
          size="lg"
          className="w-full"
        >
          {loading ? '登录中...' : '登录'}
        </ModernButton>
      </form>

      {/* 分割线 */}
      <div className="flex items-center">
        <div className="flex-1 border-t border-gray-200 dark:border-gray-700"></div>
        <span className="px-4 text-sm text-gray-500 dark:text-gray-400">或</span>
        <div className="flex-1 border-t border-gray-200 dark:border-gray-700"></div>
      </div>

      {/* Google登录 */}
      <ModernButton
        variant="outline"
        size="lg"
        className="w-full"
        loading={googleLoading}
        leftIcon={!googleLoading && <Chrome className="w-5 h-5" />}
        onClick={handleGoogleLogin}
      >
        {googleLoading ? '连接中...' : '使用 Google 登录'}
      </ModernButton>

      {/* 注册链接 */}
      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-300">
          还没有账户？{' '}
          <Link
            to="/register"
            className="text-purple-600 dark:text-purple-400 hover:text-purple-500 dark:hover:text-purple-300 font-semibold transition-colors"
          >
            立即注册
          </Link>
        </p>
      </div>
    </motion.div>
  );
} 