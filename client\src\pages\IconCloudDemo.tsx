import React from 'react'
import { IconCloud } from '../components/ui/interactive-icon-cloud'

const slugs = [
  "whatsapp",
  "facebook", 
  "instagram",
  "gmail",
  "tiktok"
]

export function IconCloudDemo() {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">Icon Cloud Demo</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* 基础测试 */}
          <div className="bg-card p-6 rounded-lg border">
            <h2 className="text-xl font-semibold mb-4">Basic Test</h2>
            <div className="h-96 border border-border rounded">
              <IconCloud iconSlugs={["react", "typescript", "javascript"]} />
            </div>
          </div>

          {/* 简单测试 */}
          <div className="bg-card p-6 rounded-lg border">
            <h2 className="text-xl font-semibold mb-4">Simple Icon Cloud</h2>
            <div className="h-96 border border-border rounded">
              <IconCloud iconSlugs={slugs} />
            </div>
          </div>

          {/* 平台集成测试 */}
          <div className="bg-card p-6 rounded-lg border">
            <h2 className="text-xl font-semibold mb-4">Platform Integrations</h2>
            <div className="h-96 border border-border rounded">
              <IconCloud iconSlugs={[
                "whatsapp",
                "messenger",
                "shopee",
                "lazada",
                "gmail",
                "facebook",
                "instagram",
                "tiktok",
                "webhook"
              ]} />
            </div>
          </div>
        </div>
        
        <div className="mt-8 text-center">
          <p className="text-muted-foreground">
            如果看到动态的3D图标云，说明组件工作正常。
            如果看到加载动画或错误信息，说明有问题需要修复。
          </p>
        </div>
      </div>
    </div>
  )
}
