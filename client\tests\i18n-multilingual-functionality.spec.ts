import { test, expect } from '@playwright/test';

test.describe('多语言功能测试 / Multilingual Functionality Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // 安全地清除本地存储以确保测试的一致性
    try {
      await page.evaluate(() => {
        if (typeof(Storage) !== "undefined") {
          localStorage.clear();
        }
      });
    } catch (error) {
      // 忽略localStorage访问错误，这在某些情况下是正常的
      console.log('localStorage clear skipped due to security restrictions');
    }
  });

  test('语言切换功能 - 主页测试', async ({ page }) => {
    // 1. 访问主页
    await page.goto('/');
    
    // 2. 验证默认语言（英文）内容
    await expect(page).toHaveTitle(/AI-Powered Content Creation Platform/);
    await expect(page.locator('h1')).toContainText(/Transform your ideas/);
    
    // 3. 找到并点击语言切换器
    const languageSwitcher = page.locator('[data-testid="language-switcher"]').first();
    if (await languageSwitcher.isVisible()) {
      await languageSwitcher.click();
    } else {
      // 如果没有测试ID，通过Globe图标查找
      const globeButton = page.locator('svg').filter({ hasText: /globe/i }).first();
      await globeButton.click({ timeout: 5000 });
    }
    
    // 4. 切换到中文
    await page.locator('text=中文').first().click();
    await page.waitForTimeout(1000); // 等待语言切换完成
    
    // 5. 验证中文内容
    await expect(page).toHaveTitle(/人工智能内容创作平台/);
    await expect(page.locator('text=将您的想法转化为')).toBeVisible();
    await expect(page.locator('text=免费开始')).toBeVisible();
    
    // 6. 切换到马来文
    const languageSwitcherCN = page.locator('text=中文').first();
    await languageSwitcherCN.click();
    await page.locator('text=Bahasa Malaysia').first().click();
    await page.waitForTimeout(1000);
    
    // 7. 验证马来文内容
    await expect(page.locator('text=Platform Penciptaan Kandungan Berkuasa AI')).toBeVisible();
    await expect(page.locator('text=Mula Percuma')).toBeVisible();
    
    // 8. 切换回英文
    const languageSwitcherMS = page.locator('text=Bahasa Malaysia').first();
    await languageSwitcherMS.click();
    await page.locator('text=English').first().click();
    await page.waitForTimeout(1000);
    
    // 9. 验证英文内容恢复
    await expect(page.locator('text=Get Started Free')).toBeVisible();
  });

  test('CTA和Footer多语言支持', async ({ page }) => {
    await page.goto('/');
    
    // 1. 验证英文CTA和Footer
    await expect(page.locator('text=Ready to Transform?')).toBeVisible();
    await expect(page.locator('text=Follow us')).toBeVisible();
    await expect(page.locator('text=© 2025 iTeraBiz. All rights reserved.')).toBeVisible();
    
    // 2. 切换到中文
    const languageSwitcher = page.locator('svg').filter({ hasText: /globe/i }).first();
    await languageSwitcher.click();
    await page.locator('text=中文').first().click();
    await page.waitForTimeout(1000);
    
    // 3. 验证中文CTA和Footer
    await expect(page.locator('text=准备好改变了吗？')).toBeVisible();
    await expect(page.locator('text=关注我们')).toBeVisible();
    await expect(page.locator('text=© 2025 iTeraBiz. 保留所有权利。')).toBeVisible();
    
    // 4. 切换到马来文
    const languageSwitcherCN = page.locator('text=中文').first();
    await languageSwitcherCN.click();
    await page.locator('text=Bahasa Malaysia').first().click();
    await page.waitForTimeout(1000);
    
    // 5. 验证马来文CTA和Footer
    await expect(page.locator('text=Bersedia untuk Mengubah?')).toBeVisible();
    await expect(page.locator('text=Ikuti kami')).toBeVisible();
    await expect(page.locator('text=© 2025 iTeraBiz. Hak cipta terpelihara.')).toBeVisible();
  });

  test('认证页面多语言支持', async ({ page }) => {
    // 1. 测试登录页面
    await page.goto('/login');
    
    // 验证英文登录页面
    await expect(page.locator('text=Welcome Back')).toBeVisible();
    await expect(page.locator('text=Sign In')).toBeVisible();
    
    // 2. 切换到中文
    const languageSwitcher = page.locator('svg').filter({ hasText: /globe/i }).first();
    if (await languageSwitcher.isVisible()) {
      await languageSwitcher.click();
      await page.locator('text=中文').first().click();
      await page.waitForTimeout(1000);
      
      // 验证中文登录页面
      await expect(page.locator('text=欢迎回来')).toBeVisible();
      await expect(page.locator('text=登录')).toBeVisible();
    }
    
    // 3. 测试注册页面
    await page.goto('/register');
    
    // 验证注册页面翻译（如果有语言切换器）
    const regLanguageSwitcher = page.locator('svg').filter({ hasText: /globe/i }).first();
    if (await regLanguageSwitcher.isVisible()) {
      await regLanguageSwitcher.click();
      await page.locator('text=English').first().click();
      await page.waitForTimeout(1000);
      
      await expect(page.locator('text=Create Your Account')).toBeVisible();
    }
  });

  test('语言设置持久化', async ({ page }) => {
    // 1. 访问主页并设置为中文
    await page.goto('/');
    
    const languageSwitcher = page.locator('svg').filter({ hasText: /globe/i }).first();
    if (await languageSwitcher.isVisible()) {
      await languageSwitcher.click();
      await page.locator('text=中文').first().click();
      await page.waitForTimeout(1000);
    }
    
    // 2. 验证页面为中文
    await expect(page.locator('text=将您的想法转化为')).toBeVisible();
    
    // 3. 刷新页面
    await page.reload();
    await page.waitForTimeout(1000);
    
    // 4. 验证语言设置保持为中文
    await expect(page.locator('text=将您的想法转化为')).toBeVisible();
    
    // 5. 验证localStorage中的语言设置
    const storedLanguage = await page.evaluate(() => {
      return localStorage.getItem('i18nextLng');
    });
    expect(storedLanguage).toBe('zh');
  });

  test('导航和链接的多语言支持', async ({ page }) => {
    await page.goto('/');
    
    // 1. 验证英文导航
    await expect(page.locator('text=Home')).toBeVisible();
    await expect(page.locator('text=Features')).toBeVisible();
    await expect(page.locator('text=Pricing')).toBeVisible();
    
    // 2. 切换到中文
    const languageSwitcher = page.locator('svg').filter({ hasText: /globe/i }).first();
    if (await languageSwitcher.isVisible()) {
      await languageSwitcher.click();
      await page.locator('text=中文').first().click();
      await page.waitForTimeout(1000);
      
      // 3. 验证中文导航
      await expect(page.locator('text=首页')).toBeVisible();
      await expect(page.locator('text=功能')).toBeVisible();
      await expect(page.locator('text=价格')).toBeVisible();
    }
  });

  test('价格方案多语言支持', async ({ page }) => {
    await page.goto('/');
    
    // 滚动到价格区域
    await page.locator('#pricing').scrollIntoViewIfNeeded();
    
    // 1. 验证英文价格方案
    await expect(page.locator('text=Choose Your Perfect Plan')).toBeVisible();
    await expect(page.locator('text=STARTER')).toBeVisible();
    await expect(page.locator('text=PROFESSIONAL')).toBeVisible();
    await expect(page.locator('text=ENTERPRISE')).toBeVisible();
    
    // 2. 切换到中文
    const languageSwitcher = page.locator('svg').filter({ hasText: /globe/i }).first();
    if (await languageSwitcher.isVisible()) {
      await languageSwitcher.click();
      await page.locator('text=中文').first().click();
      await page.waitForTimeout(1000);
      
      // 3. 验证中文价格方案
      await expect(page.locator('text=选择您的完美计划')).toBeVisible();
      await expect(page.locator('text=入门版')).toBeVisible();
      await expect(page.locator('text=专业版')).toBeVisible();
      await expect(page.locator('text=企业版')).toBeVisible();
    }
  });

  test('表单和输入框的多语言支持', async ({ page }) => {
    // 如果有搜索或联系表单，测试其多语言支持
    await page.goto('/');
    
    // 查找可能的搜索框或输入框
    const searchInput = page.locator('input[placeholder*="search"], input[placeholder*="搜索"]').first();
    
    if (await searchInput.isVisible()) {
      // 验证placeholder的多语言支持
      const placeholderText = await searchInput.getAttribute('placeholder');
      expect(placeholderText).toBeTruthy();
      
      // 切换语言后验证placeholder是否改变
      const languageSwitcher = page.locator('svg').filter({ hasText: /globe/i }).first();
      if (await languageSwitcher.isVisible()) {
        await languageSwitcher.click();
        await page.locator('text=中文').first().click();
        await page.waitForTimeout(1000);
        
        const newPlaceholderText = await searchInput.getAttribute('placeholder');
        // placeholder应该已经改变（如果实现了翻译的话）
        expect(newPlaceholderText).toBeTruthy();
      }
    }
  });

  test('错误状态的多语言支持', async ({ page }) => {
    // 测试404页面的多语言支持
    await page.goto('/non-existent-page');
    
    // 验证404页面是否有多语言支持
    const languageSwitcher = page.locator('svg').filter({ hasText: /globe/i }).first();
    if (await languageSwitcher.isVisible()) {
      // 如果有语言切换器，测试多语言支持
      await languageSwitcher.click();
      await page.locator('text=中文').first().click();
      await page.waitForTimeout(1000);
      
      // 验证是否有中文的404内容（如果实现了的话）
      const pageContent = await page.textContent('body');
      expect(pageContent).toBeTruthy();
    }
  });

  test('响应式设计中的语言切换', async ({ page }) => {
    // 测试移动端的语言切换
    await page.setViewportSize({ width: 375, height: 667 }); // iPhone尺寸
    await page.goto('/');
    
    // 在移动端查找语言切换器
    const mobileLanguageSwitcher = page.locator('svg').filter({ hasText: /globe/i }).first();
    
    if (await mobileLanguageSwitcher.isVisible()) {
      await mobileLanguageSwitcher.click();
      await page.locator('text=中文').first().click();
      await page.waitForTimeout(1000);
      
      // 验证移动端的中文显示
      await expect(page.locator('text=将您的想法转化为')).toBeVisible();
    }
    
    // 恢复桌面尺寸
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('URL路由的多语言兼容性', async ({ page }) => {
    // 测试不同语言设置下的路由导航
    await page.goto('/');
    
    // 设置为中文
    const languageSwitcher = page.locator('svg').filter({ hasText: /globe/i }).first();
    if (await languageSwitcher.isVisible()) {
      await languageSwitcher.click();
      await page.locator('text=中文').first().click();
      await page.waitForTimeout(1000);
    }
    
    // 导航到不同页面，验证语言设置保持
    const routes = ['/login', '/register', '/'];
    
    for (const route of routes) {
      await page.goto(route);
      await page.waitForTimeout(500);
      
      // 验证页面仍然是中文
      const currentLanguage = await page.evaluate(() => {
        return localStorage.getItem('i18nextLng');
      });
      expect(currentLanguage).toBe('zh');
    }
  });
}); 