// 🌐 IteraBiz国际化配置
// 支持英文（默认）、中文、马来语

export interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  rtl?: boolean;
}

export const supportedLanguages: Language[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  },
  {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳'
  },
  {
    code: 'ms',
    name: 'Malay',
    nativeName: 'Bahasa Melayu',
    flag: '🇲🇾'
  }
];

export const defaultLanguage = 'en';

export const getLanguageByCode = (code: string): Language | undefined => {
  return supportedLanguages.find(lang => lang.code === code);
};

export const getBrowserLanguage = (): string => {
  const browserLang = navigator.language.split('-')[0];
  const supportedCodes = supportedLanguages.map(lang => lang.code);
  return supportedCodes.includes(browserLang) ? browserLang : defaultLanguage;
};

// 语言存储键
export const LANGUAGE_STORAGE_KEY = 'iterabiz_language';

// 获取当前语言
export const getCurrentLanguage = (): string => {
  const stored = localStorage.getItem(LANGUAGE_STORAGE_KEY);
  if (stored && supportedLanguages.some(lang => lang.code === stored)) {
    return stored;
  }
  return getBrowserLanguage();
};

// 设置语言
export const setLanguage = (code: string): void => {
  if (supportedLanguages.some(lang => lang.code === code)) {
    localStorage.setItem(LANGUAGE_STORAGE_KEY, code);
    // 触发语言变更事件
    window.dispatchEvent(new CustomEvent('languageChange', { detail: code }));
  }
};
