import { test, expect } from '@playwright/test';

test.describe('Enhanced Register Page', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到注册页面
    await page.goto('http://localhost:3003/register');
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
  });

  test('应该正确显示页面标题和logo', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/Create Your Account - IteraBiz/);
    
    // 检查页面标题文本
    await expect(page.locator('h1')).toContainText('Create Your Account');
    
    // 检查副标题
    await expect(page.locator('text=Join IteraBiz and start your AI-powered content creation journey')).toBeVisible();
    
    // 检查logo图片是否存在
    const logoImg = page.locator('img[alt="IteraBiz Logo"]');
    await expect(logoImg).toBeVisible();
    await expect(logoImg).toHaveAttribute('src', '/newicon.png');
    
    // 检查IteraBiz品牌文字（在logo区域）
    await expect(page.locator('a[href="/home"] span:has-text("IteraBiz")')).toBeVisible();
  });

  test('应该正确显示Back to Home按钮并且功能正常', async ({ page }) => {
    // 检查Back to Home按钮是否存在
    const backButton = page.locator('a:has-text("Back to Home")');
    await expect(backButton).toBeVisible();
    
    // 检查按钮的href属性
    await expect(backButton).toHaveAttribute('href', '/home');
    
    // 检查按钮包含箭头图标
    await expect(backButton.locator('svg')).toBeVisible();
  });

  test('应该正确显示进度指示器', async ({ page }) => {
    // 检查进度指示器
    await expect(page.locator('text=Step 1 of 2')).toBeVisible();
    await expect(page.locator('text=Personal Information')).toBeVisible();
    
    // 检查进度条
    const progressBar = page.locator('.bg-purple-600');
    await expect(progressBar).toBeVisible();
  });

  test('应该正确显示第一步表单字段', async ({ page }) => {
    // 检查First Name字段
    await expect(page.locator('input[placeholder="John"]')).toBeVisible();
    await expect(page.locator('text=First Name')).toBeVisible();
    
    // 检查Last Name字段
    await expect(page.locator('input[placeholder="Doe"]')).toBeVisible();
    await expect(page.locator('text=Last Name')).toBeVisible();
    
    // 检查Email字段
    await expect(page.locator('input[placeholder="<EMAIL>"]')).toBeVisible();
    await expect(page.locator('text=Email Address')).toBeVisible();
    
    // 检查Company字段
    await expect(page.locator('input[placeholder="Your company name"]')).toBeVisible();
    await expect(page.locator('text=Company (Optional)')).toBeVisible();
    
    // 检查Continue按钮
    await expect(page.locator('button:has-text("Continue")')).toBeVisible();
  });

  test('应该正确显示Google注册选项', async ({ page }) => {
    // 检查分隔线文字
    await expect(page.locator('text=Or continue with')).toBeVisible();
    
    // 检查Google注册按钮
    await expect(page.locator('button:has-text("Sign up with Google")')).toBeVisible();
  });

  test('应该正确显示登录链接', async ({ page }) => {
    // 检查登录链接文字
    await expect(page.locator('text=Already have an account?')).toBeVisible();
    
    // 检查登录链接
    const signInLink = page.locator('a:has-text("Sign in")');
    await expect(signInLink).toBeVisible();
    await expect(signInLink).toHaveAttribute('href', '/login');
  });

  test('应该能够填写第一步表单并进入第二步', async ({ page }) => {
    // 填写第一步表单
    await page.fill('input[placeholder="John"]', 'Test');
    await page.fill('input[placeholder="Doe"]', 'User');
    await page.fill('input[placeholder="<EMAIL>"]', '<EMAIL>');
    await page.fill('input[placeholder="Your company name"]', 'Test Company');
    
    // 点击Continue按钮
    await page.click('button:has-text("Continue")');
    
    // 等待动画完成
    await page.waitForTimeout(500);
    
    // 检查是否进入第二步
    await expect(page.locator('text=Step 2 of 2')).toBeVisible();
    await expect(page.locator('text=Security & Terms')).toBeVisible();
  });

  test('应该正确显示第二步表单字段', async ({ page }) => {
    // 先进入第二步
    await page.fill('input[placeholder="John"]', 'Test');
    await page.fill('input[placeholder="Doe"]', 'User');
    await page.fill('input[placeholder="<EMAIL>"]', '<EMAIL>');
    await page.click('button:has-text("Continue")');
    await page.waitForTimeout(500);
    
    // 检查密码字段
    await expect(page.locator('input[placeholder="Create a strong password"]')).toBeVisible();
    await expect(page.locator('label:has-text("Password"):not(:has-text("Confirm"))')).toBeVisible();
    
    // 检查确认密码字段
    await expect(page.locator('input[placeholder="Confirm your password"]')).toBeVisible();
    await expect(page.locator('text=Confirm Password')).toBeVisible();
    
    // 检查条款复选框
    await expect(page.locator('text=I agree to the Terms of Service and Privacy Policy')).toBeVisible();
    
    // 检查营销复选框
    await expect(page.locator('text=I\'d like to receive product updates and marketing communications')).toBeVisible();
    
    // 检查Back按钮
    await expect(page.locator('button:has-text("Back")')).toBeVisible();
    
    // 检查Create Account按钮
    await expect(page.locator('button:has-text("Create Account")')).toBeVisible();
  });

  test('应该能够从第二步返回第一步', async ({ page }) => {
    // 先进入第二步
    await page.fill('input[placeholder="John"]', 'Test');
    await page.fill('input[placeholder="Doe"]', 'User');
    await page.fill('input[placeholder="<EMAIL>"]', '<EMAIL>');
    await page.click('button:has-text("Continue")');
    await page.waitForTimeout(500);
    
    // 点击Back按钮
    await page.click('button:has-text("Back")');
    await page.waitForTimeout(500);
    
    // 检查是否回到第一步
    await expect(page.locator('text=Step 1 of 2')).toBeVisible();
    await expect(page.locator('text=Personal Information')).toBeVisible();
    
    // 检查表单数据是否保留
    await expect(page.locator('input[placeholder="John"]')).toHaveValue('Test');
    await expect(page.locator('input[placeholder="Doe"]')).toHaveValue('User');
    await expect(page.locator('input[placeholder="<EMAIL>"]')).toHaveValue('<EMAIL>');
  });

  test('应该正确显示页面布局和间距', async ({ page }) => {
    // 检查卡片容器的最大宽度
    const card = page.locator('[class*="Card"]:has(.max-w-lg)');
    await expect(card).toBeVisible();
    
    // 检查页面不会过于拥挤
    const cardHeader = page.locator('[class*="CardHeader"]');
    await expect(cardHeader).toBeVisible();
    
    // 检查进度指示器间距
    const progressContainer = page.locator('text=Step 1 of 2').locator('..');
    await expect(progressContainer).toBeVisible();
  });

  test('应该正确显示品牌信息', async ({ page }) => {
    // 检查版权信息
    await expect(page.locator('text=© 2025 IteraBiz. All rights reserved.')).toBeVisible();
    
    // 检查隐私政策链接
    await expect(page.locator('a:has-text("Privacy Policy")')).toBeVisible();
    
    // 检查服务条款链接
    await expect(page.locator('a:has-text("Terms of Service")')).toBeVisible();
  });
});
