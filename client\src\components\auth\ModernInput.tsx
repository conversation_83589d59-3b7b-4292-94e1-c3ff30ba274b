import React, { useState, forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { Eye, EyeOff } from 'lucide-react';

interface ModernInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  showPasswordToggle?: boolean;
  leftIcon?: React.ReactNode;
}

export const ModernInput = forwardRef<HTMLInputElement, ModernInputProps>(
  ({ className, label, error, type, showPasswordToggle, leftIcon, ...props }, ref) => {
    const [isFocused, setIsFocused] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [hasValue, setHasValue] = useState(false);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(e.target.value.length > 0);
      props.onChange?.(e);
    };

    const inputType = showPasswordToggle && type === 'password' 
      ? (showPassword ? 'text' : 'password') 
      : type;

    return (
      <div className="relative">
        <div className="relative">
          {/* 左侧图标 */}
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400 z-10">
              {leftIcon}
            </div>
          )}
          
          <input
            type={inputType}
            className={cn(
              "peer w-full pt-6 pb-2 text-gray-900 dark:text-white bg-transparent border-2 rounded-lg transition-all duration-200",
              "border-gray-200 dark:border-gray-700",
              "focus:border-purple-500 dark:focus:border-purple-400 focus:outline-none",
              "placeholder-transparent",
              error && "border-red-500 dark:border-red-400",
              leftIcon ? "pl-12 pr-4" : "px-4",
              className
            )}
            placeholder={label}
            ref={ref}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            onChange={handleInputChange}
            {...props}
          />
          
          {/* 浮动标签 */}
          <label className={cn(
            "absolute transition-all duration-200 pointer-events-none",
            "text-gray-500 dark:text-gray-400",
            leftIcon ? "left-12" : "left-4",
            (isFocused || hasValue || props.value) 
              ? "top-2 text-xs text-purple-600 dark:text-purple-400" 
              : "top-4 text-base"
          )}>
            {label}
          </label>
          
          {/* 密码可见性切换 */}
          {showPasswordToggle && type === 'password' && (
            <button
              type="button"
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          )}
        </div>
        
        {/* 错误信息 */}
        {error && (
          <p className="mt-2 text-sm text-red-600 dark:text-red-400 animate-in slide-in-from-top-2 duration-200">
            {error}
          </p>
        )}
      </div>
    );
  }
); 