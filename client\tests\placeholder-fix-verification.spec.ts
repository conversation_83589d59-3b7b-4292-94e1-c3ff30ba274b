import { test, expect } from '@playwright/test';

test.describe('占位符与图标叠加修复验证', () => {
  test('登录页面 - 验证占位符与图标不再叠加', async ({ page }) => {
    await page.goto('http://localhost:3003/login');
    await page.waitForLoadState('networkidle');
    
    // 检查邮箱输入框的样式
    const emailInput = page.locator('input[type="email"]');
    const emailStyles = await emailInput.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        paddingLeft: styles.paddingLeft,
        paddingRight: styles.paddingRight
      };
    });
    
    console.log('Email Input Styles:', emailStyles);
    
    // 验证左内边距足够大，不会与图标重叠
    const paddingLeftValue = parseInt(emailStyles.paddingLeft);
    expect(paddingLeftValue).toBeGreaterThanOrEqual(40); // 至少40px，确保不与图标重叠
    
    // 检查密码输入框的样式
    const passwordInput = page.locator('input[type="password"]');
    const passwordStyles = await passwordInput.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        paddingLeft: styles.paddingLeft,
        paddingRight: styles.paddingRight
      };
    });
    
    console.log('Password Input Styles:', passwordStyles);
    
    // 验证左内边距足够大，不会与图标重叠
    const passwordPaddingLeftValue = parseInt(passwordStyles.paddingLeft);
    expect(passwordPaddingLeftValue).toBeGreaterThanOrEqual(40);
    
    // 截图验证视觉效果
    await page.screenshot({ path: 'test-results/login-placeholder-fixed.png' });
  });

  test('注册页面第一步 - 验证有图标和无图标输入框的样式', async ({ page }) => {
    await page.goto('http://localhost:3003/register');
    await page.waitForLoadState('networkidle');
    
    // 检查有图标的输入框（First Name, Last Name, Email, Company）
    const inputsWithIcons = [
      { selector: 'input[placeholder="First name"]', name: 'First Name' },
      { selector: 'input[placeholder="Last name"]', name: 'Last Name' },
      { selector: 'input[placeholder="<EMAIL>"]', name: 'Email' },
      { selector: 'input[placeholder="Company name (optional)"]', name: 'Company' }
    ];
    
    for (const inputInfo of inputsWithIcons) {
      const input = page.locator(inputInfo.selector);
      const styles = await input.evaluate(el => {
        const computedStyles = window.getComputedStyle(el);
        return {
          paddingLeft: computedStyles.paddingLeft,
          paddingRight: computedStyles.paddingRight
        };
      });
      
      console.log(`${inputInfo.name} Input Styles:`, styles);
      
      // 验证左内边距
      const paddingLeftValue = parseInt(styles.paddingLeft);
      expect(paddingLeftValue).toBeGreaterThanOrEqual(40);
    }
    
    // 截图验证视觉效果
    await page.screenshot({ path: 'test-results/register-step1-placeholder-fixed.png' });
  });

  test('注册页面第二步 - 验证密码输入框样式', async ({ page }) => {
    await page.goto('http://localhost:3003/register');
    await page.waitForLoadState('networkidle');
    
    // 填写第一步表单
    await page.fill('input[placeholder="First name"]', 'Test');
    await page.fill('input[placeholder="Last name"]', 'User');
    await page.fill('input[placeholder="<EMAIL>"]', '<EMAIL>');
    
    // 点击Continue按钮进入第二步
    await page.click('button:has-text("Continue")');
    await page.waitForTimeout(1000);
    
    // 检查密码输入框
    const passwordInputs = page.locator('input[placeholder="••••••••"]');
    await expect(passwordInputs).toHaveCount(2);
    
    // 检查第一个密码输入框的样式
    const passwordInput = passwordInputs.first();
    const passwordStyles = await passwordInput.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        paddingLeft: styles.paddingLeft,
        paddingRight: styles.paddingRight
      };
    });
    
    console.log('Password Input Styles (Step 2):', passwordStyles);
    
    // 验证左内边距
    const paddingLeftValue = parseInt(passwordStyles.paddingLeft);
    expect(paddingLeftValue).toBeGreaterThanOrEqual(40);
    
    // 截图验证视觉效果
    await page.screenshot({ path: 'test-results/register-step2-placeholder-fixed.png' });
  });

  test('忘记密码页面 - 验证邮箱输入框样式', async ({ page }) => {
    await page.goto('http://localhost:3003/forgot-password');
    await page.waitForLoadState('networkidle');
    
    // 检查邮箱输入框
    const emailInput = page.locator('input[placeholder="<EMAIL>"]');
    const emailStyles = await emailInput.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        paddingLeft: styles.paddingLeft,
        paddingRight: styles.paddingRight
      };
    });
    
    console.log('Forgot Password Email Input Styles:', emailStyles);
    
    // 验证左内边距
    const paddingLeftValue = parseInt(emailStyles.paddingLeft);
    expect(paddingLeftValue).toBeGreaterThanOrEqual(40);
    
    // 截图验证视觉效果
    await page.screenshot({ path: 'test-results/forgot-password-placeholder-fixed.png' });
  });

  test('验证图标位置与输入框内边距的协调性', async ({ page }) => {
    await page.goto('http://localhost:3003/login');
    await page.waitForLoadState('networkidle');
    
    // 获取邮箱输入框和图标的位置
    const emailInput = page.locator('input[type="email"]');
    const emailContainer = emailInput.locator('..');
    const emailIcon = emailContainer.locator('div').first();
    
    const inputBox = await emailInput.boundingBox();
    const iconBox = await emailIcon.boundingBox();
    
    console.log('Input Box:', inputBox);
    console.log('Icon Box:', iconBox);
    
    if (inputBox && iconBox) {
      // 验证图标在输入框内部，且不与文字重叠
      expect(iconBox.x).toBeGreaterThan(inputBox.x);
      expect(iconBox.x + iconBox.width).toBeLessThan(inputBox.x + inputBox.width);
      
      // 验证图标与输入框左边缘的距离合理（应该在12px左右）
      const iconLeftMargin = iconBox.x - inputBox.x;
      expect(iconLeftMargin).toBeGreaterThanOrEqual(10);
      expect(iconLeftMargin).toBeLessThanOrEqual(15);
      
      // 验证输入框文字区域开始位置在图标右侧
      const textStartPosition = iconBox.x + iconBox.width + 8; // 图标右侧 + 8px间距
      const inputPaddingLeft = await emailInput.evaluate(el => {
        return parseInt(window.getComputedStyle(el).paddingLeft);
      });
      const expectedTextStart = inputBox.x + inputPaddingLeft;
      
      expect(expectedTextStart).toBeGreaterThanOrEqual(textStartPosition);
    }
  });
});
