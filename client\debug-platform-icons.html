<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Platform Icons Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .icon-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .icon-card img {
            width: 64px;
            height: 64px;
            margin-bottom: 10px;
        }
        .icon-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .icon-url {
            font-size: 12px;
            color: #666;
            word-break: break-all;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-top: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🔧 Platform Icons Debug Tool</h1>
    <p>Testing platform icons loading from different sources...</p>
    
    <div class="icon-grid" id="iconGrid">
        <!-- Icons will be loaded here -->
    </div>

    <script>
        const platformIcons = [
            { slug: "gmail", type: "simple", name: "Gmail" },
            { slug: "lazada", type: "img", src: "https://logosarchive.com/wp-content/uploads/2021/05/Lazada-icon.svg", name: "Lazada" },
            { slug: "shopee", type: "simple", name: "Shopee" },
            { slug: "tiktok", type: "simple", name: "TikTok" },
            { slug: "messenger", type: "simple", name: "Facebook Messenger" },
            { slug: "whatsapp", type: "simple", name: "WhatsApp" },
            { slug: "instagram", type: "simple", name: "Instagram" },
            { slug: "facebook", type: "simple", name: "Facebook" },
            { slug: "webhook", type: "simple", name: "Webhook (Test)" }
        ];

        function createIconCard(icon) {
            const card = document.createElement('div');
            card.className = 'icon-card';
            
            const iconName = document.createElement('div');
            iconName.className = 'icon-name';
            iconName.textContent = icon.name;
            
            const img = document.createElement('img');
            const status = document.createElement('div');
            status.className = 'status';
            
            if (icon.type === 'img') {
                img.src = icon.src;
                img.alt = icon.name;
            } else {
                img.src = `https://cdn.simpleicons.org/${icon.slug}`;
                img.alt = icon.name;
            }
            
            const url = document.createElement('div');
            url.className = 'icon-url';
            url.textContent = img.src;
            
            img.onload = () => {
                status.textContent = '✅ Loaded';
                status.className = 'status success';
            };
            
            img.onerror = () => {
                status.textContent = '❌ Failed';
                status.className = 'status error';
                img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEw0NCA0NE0yMCA0NEw0NCAyMCIgc3Ryb2tlPSIjOUI5QkEwIiBzdHJva2Utd2lkdGg9IjIiLz4KPC9zdmc+';
            };
            
            card.appendChild(iconName);
            card.appendChild(img);
            card.appendChild(url);
            card.appendChild(status);
            
            return card;
        }

        // Load all icons
        const iconGrid = document.getElementById('iconGrid');
        platformIcons.forEach(icon => {
            iconGrid.appendChild(createIconCard(icon));
        });
    </script>
</body>
</html>
