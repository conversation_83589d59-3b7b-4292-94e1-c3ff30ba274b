import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { authTokensV2, googleIconPaths } from '@/design-system/tokens/auth-tokens';

interface GoogleSignInButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  loading?: boolean;
  variant?: 'default' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  onGoogleSignIn?: () => void;
}

// Google图标组件 (原生设计)
const GoogleIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg viewBox="0 0 24 24" className={cn("w-5 h-5", className)}>
    <path fill="#4285F4" d={googleIconPaths.blue} />
    <path fill="#34A853" d={googleIconPaths.green} />
    <path fill="#FBBC05" d={googleIconPaths.yellow} />
    <path fill="#EA4335" d={googleIconPaths.red} />
  </svg>
);

// 加载动画组件
const LoadingSpinner: React.FC<{ className?: string }> = ({ className }) => (
  <motion.div
    animate={{ rotate: 360 }}
    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    className={cn("w-5 h-5 border-2 border-gray-300 border-t-gray-600 rounded-full", className)}
  />
);

const GoogleSignInButton = React.memo(React.forwardRef<HTMLButtonElement, GoogleSignInButtonProps>(
  ({ 
    loading = false,
    variant = 'default',
    size = 'md',
    fullWidth = true,
    onGoogleSignIn,
    className,
    children,
    disabled,
    ...props 
  }, ref) => {
    const isDisabled = disabled || loading;

    // 尺寸变体 (响应式)
    const sizeVariants = {
      sm: "py-2 px-4 text-sm sm:py-2.5 sm:px-5",
      md: "py-3 px-4 text-base sm:py-3.5 sm:px-5",
      lg: "py-4 px-6 text-lg sm:py-4 sm:px-7"
    };

    // 样式变体 (Google原生设计规范 + 深色主题)
    const variantStyles = {
      default: cn(
        // 基础样式
        "bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 font-medium",
        "hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500",
        "focus:bg-gray-50 dark:focus:bg-gray-700 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800",
        "active:bg-gray-100 dark:active:bg-gray-600",
        // 阴影 (Google原生)
        "shadow-sm hover:shadow-md",
        // 过渡
        "transition-all duration-200 ease-out"
      ),
      outline: cn(
        "bg-transparent border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 font-medium",
        "hover:bg-gray-50 dark:hover:bg-gray-800 hover:border-gray-400 dark:hover:border-gray-500",
        "focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800",
        "transition-all duration-200 ease-out"
      )
    };

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (isDisabled) return;
      
      if (onGoogleSignIn) {
        onGoogleSignIn();
      }
      
      if (props.onClick) {
        props.onClick(e);
      }
    };

    return (
      <motion.button
        ref={ref}
        type="button"
        className={cn(
          // 基础布局
          "inline-flex items-center justify-center gap-3 rounded-lg",
          "disabled:opacity-60 disabled:cursor-not-allowed",
          "relative overflow-hidden",
          
          // 尺寸
          sizeVariants[size],
          
          // 宽度
          fullWidth && "w-full",
          
          // 变体样式
          variantStyles[variant],
          
          // 禁用状态
          isDisabled && "hover:bg-white hover:border-gray-300 hover:shadow-sm",
          
          className
        )}
        disabled={isDisabled}
        onClick={handleClick}
        whileTap={!isDisabled ? { scale: 0.98 } : {}}
        whileHover={!isDisabled ? { scale: 1.01 } : {}}
        transition={{ duration: 0.15, ease: "easeOut" }}
        {...props}
      >
        {/* 图标 */}
        <span className="flex-shrink-0">
          {loading ? (
            <LoadingSpinner />
          ) : (
            <GoogleIcon />
          )}
        </span>
        
        {/* 文本 */}
        <span className="flex-1 text-center">
          {children || (loading ? "Signing in..." : "Continue with Google")}
        </span>
      </motion.button>
    );
  }
));

GoogleSignInButton.displayName = "GoogleSignInButton";

export { GoogleSignInButton, GoogleIcon };
export type { GoogleSignInButtonProps };
