import React from 'react'
import { cn } from '../../../lib/utils';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'gradient';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  isLoading?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode
};

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(({ 
    className, 
    variant = 'primary',
    size = 'md',
    isLoading = false,
    icon,
    children, 
    disabled,
    ...props 
  }, ref) => {
    const baseStyles = [
      'inline-flex items-center justify-center',
      'font-medium text-center',
      'transition-all duration-300 ease-out',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'whitespace-nowrap',
    ];

    const variants = {
      primary: [
        'bg-primary-500 text-white',
        'hover:bg-primary-600 hover:shadow-marketing-purple hover:-translate-y-0.5',
        'focus:ring-primary-500/50',
        'active:bg-primary-700',
      ],
      secondary: [
        'bg-white text-text-primary border border-border-default',
        'hover:bg-bg-accent hover:shadow-marketing-card hover:-translate-y-0.5',
        'focus:ring-primary-500/30',
        'active:bg-primary-50',
      ],
      outline: [
        'bg-transparent text-primary-500 border border-primary-500',
        'hover:bg-primary-500 hover:text-white hover:shadow-marketing-purple hover:-translate-y-0.5',
        'focus:ring-primary-500/50',
        'active:bg-primary-600',
      ],
      ghost: [
        'bg-transparent text-primary-500',
        'hover:bg-primary-600 hover:text-white',
        'focus:ring-primary-500/30',
        'active:bg-primary-700',
      ],
      gradient: [
        'bg-gradient-primary text-white',
        'hover:shadow-marketing-large hover:-translate-y-1',
        'focus:ring-primary-500/50',
        'active:scale-95',
        'relative overflow-hidden',
        'before:absolute before:inset-0 before:bg-gradient-to-r before:from-primary-600 before:to-primary-700',
        'before:opacity-0 before:transition-opacity before:duration-300',
        'hover:before:opacity-100',
      ],
    };

    const sizes = {
      sm: ['px-3 py-1.5 text-sm', 'gap-1.5', 'rounded-md'],
      md: ['px-4 py-2 text-base', 'gap-2', 'rounded-lg'],
      lg: ['px-6 py-3 text-lg', 'gap-2.5', 'rounded-lg'],
      xl: ['px-8 py-4 text-xl', 'gap-3', 'rounded-xl'],
    };

    const iconSizes = {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
      xl: 'w-7 h-7',
    };

    return (
      <button
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          disabled && 'opacity-50 cursor-not-allowed hover:transform-none hover:shadow-none',
          className
        )}
        disabled={disabled || isLoading}
        {...props}
      >
        {variant === 'gradient' && (
          <span className="relative z-10 flex items-center gap-2">
            {isLoading ? (
              <div className={cn("animate-spin rounded-full border-2 border-white/30 border-t-white", iconSizes[size])} />
            ) : icon ? (
              <span className={cn("flex-shrink-0", iconSizes[size])}>{icon}</span>
            ) : null}
            {children}
          </span>
        )}
        
        {variant !== 'gradient' && (
          <>
            {isLoading ? (
              <div className={cn("animate-spin rounded-full border-2 border-current/30 border-t-current", iconSizes[size])} />
            ) : icon ? (
              <span className={cn("flex-shrink-0", iconSizes[size])}>{icon}</span>
            ) : null}
            {children}
          </>
        )}
      </button>
    )
});

Button.displayName = 'Button'

export default Button;