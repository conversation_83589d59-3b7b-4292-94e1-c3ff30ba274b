import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { toast } from 'react-toastify';
import { 
  EnhancedAuthLayout, 
  EnhancedAuthInput, 
  EnhancedAuthButton 
} from '../../components/ui/auth';
import { 
  Mail, 
  ArrowLeft, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  HelpCircle,
  Clock
} from 'lucide-react';
import { useTranslation } from '../../i18n/useTranslation';

interface ForgotPasswordState {
  email: string;
  emailSent: boolean;
  loading: boolean;
  resendLoading: boolean;
  error: string;
  resendCount: number;
  lastResendTime: number | null;
}

export default function EnhancedForgotPasswordPage() {
  const { resetPassword } = useAuth();
  const { t } = useTranslation();
  
  const [state, setState] = useState<ForgotPasswordState>({
    email: '',
    emailSent: false,
    loading: false,
    resendLoading: false,
    error: '',
    resendCount: 0,
    lastResendTime: null
  });

  // 邮箱验证
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // 处理密码重置请求
  const handleResetRequest = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!state.email) {
      setState(prev => ({ ...prev, error: t('errors.required') }));
      return;
    }
    
    if (!validateEmail(state.email)) {
      setState(prev => ({ ...prev, error: t('errors.invalidEmail') }));
      return;
    }
    
    setState(prev => ({ ...prev, loading: true, error: '' }));
    
    try {
      const { success, error } = await resetPassword(state.email);
      
      if (!success) {
        const errorMessage = error || t('forgotPassword.resetFailed');
        setState(prev => ({ 
          ...prev, 
          error: errorMessage,
          loading: false 
        }));
        toast.error(errorMessage);
      } else {
        setState(prev => ({ 
          ...prev, 
          emailSent: true, 
          loading: false,
          resendCount: 1,
          lastResendTime: Date.now()
        }));
        toast.success(t('forgotPassword.resetSuccessful'));
      }
    } catch (error) {
      console.error('Reset password error:', error);
      const errorMessage = t('errors.generalError');
      setState(prev => ({ ...prev, error: errorMessage, loading: false }));
      toast.error(errorMessage);
    }
  };

  // 处理重新发送
  const handleResend = async () => {
    const now = Date.now();
    const timeSinceLastResend = state.lastResendTime ? now - state.lastResendTime : 0;
    const cooldownTime = 60000; // 1分钟冷却时间
    
    if (timeSinceLastResend < cooldownTime) {
      const remainingTime = Math.ceil((cooldownTime - timeSinceLastResend) / 1000);
      toast.warning(`${t('common.wait')} ${remainingTime} ${t('common.seconds')} ${t('common.beforeResending')}`);
      return;
    }
    
    setState(prev => ({ ...prev, resendLoading: true, error: '' }));
    
    try {
      const { success, error } = await resetPassword(state.email);
      
      if (!success) {
        const errorMessage = error || t('forgotPassword.resetFailed');
        setState(prev => ({ 
          ...prev, 
          error: errorMessage,
          resendLoading: false 
        }));
        toast.error(errorMessage);
      } else {
        setState(prev => ({ 
          ...prev, 
          resendLoading: false,
          resendCount: prev.resendCount + 1,
          lastResendTime: now
        }));
        toast.success(t('forgotPassword.resetSuccessful'));
      }
    } catch (error) {
      console.error('Resend error:', error);
      setState(prev => ({ 
        ...prev, 
        error: t('errors.generalError'),
        resendLoading: false 
      }));
      toast.error(t('errors.generalError'));
    }
  };

  // 处理输入变化
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setState(prev => ({ 
      ...prev, 
      email: e.target.value,
      error: '' // 清除错误
    }));
  };

  // 重新开始流程
  const handleStartOver = () => {
    setState({
      email: '',
      emailSent: false,
      loading: false,
      resendLoading: false,
      error: '',
      resendCount: 0,
      lastResendTime: null
    });
  };

  // 邮箱输入视图
  const EmailInputView = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.4 }}
      className="space-y-6"
    >
      {/* 错误信息 */}
      <AnimatePresence>
        {state.error && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="p-4 bg-red-50 border border-red-200 rounded-xl flex items-center gap-3"
          >
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
            <p className="text-sm text-red-700">{state.error}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 说明文字 */}
      <div className="text-center space-y-2">
        <p className="text-gray-600">
          {t('forgotPassword.description')}
        </p>
      </div>

      {/* 表单 */}
      <form onSubmit={handleResetRequest} className="space-y-6">
        <EnhancedAuthInput
          type="email"
          label={t('common.email')}
          placeholder={t('login.emailPlaceholder')}
          value={state.email}
          onChange={handleEmailChange}
          error={state.error && !validateEmail(state.email) ? state.error : undefined}
          leftIcon={<Mail className="w-5 h-5" />}
          variant="default"
          size="md"
          required
        />

        <EnhancedAuthButton
          type="submit"
          variant="primary"
          size="md"
          loading={state.loading}
          fullWidth={true}
          gradient={true}
          disabled={state.loading}
        >
          {state.loading ? t('forgotPassword.sendingResetLink') : t('forgotPassword.sendResetLink')}
        </EnhancedAuthButton>
      </form>

      {/* 帮助信息 */}
      <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-xl">
        <div className="flex items-start gap-3">
          <HelpCircle className="w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5" />
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-blue-900">{t('common.needHelp')}</h4>
            <p className="text-sm text-blue-700">
              {t('forgotPassword.didntReceiveEmail')}{' '}
              <Link to="/contact" className="font-medium underline">
                {t('common.contactSupport')}
              </Link>.
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );

  // 成功视图
  const SuccessView = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.4 }}
      className="space-y-6 text-center"
    >
      {/* 成功图标 */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5, type: 'spring', stiffness: 150 }}
        className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto"
      >
        <CheckCircle className="w-12 h-12 text-green-500" />
      </motion.div>

      {/* 成功信息 */}
      <h3 className="text-2xl font-bold text-gray-900">{t('forgotPassword.checkEmail')}</h3>
      <p className="text-gray-600 max-w-md mx-auto">
        {t('forgotPassword.emailSentTo')}{' '}
        <strong className="text-gray-800">{state.email}</strong>. {' '}
        {t('forgotPassword.followInstructions')}
      </p>

      {/* 重新发送按钮 */}
      <div className="flex flex-col items-center gap-4">
        <EnhancedAuthButton
          type="button"
          variant="primary"
          size="md"
          loading={state.resendLoading}
          onClick={handleResend}
          gradient={true}
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${state.resendLoading ? 'animate-spin' : ''}`} />
          {state.resendLoading ? t('forgotPassword.resending') : t('forgotPassword.resendEmail')}
        </EnhancedAuthButton>
        <span className="text-xs text-gray-400">
          {t('forgotPassword.emailSentTimes')}: {state.resendCount}
        </span>
      </div>
      
      {/* 帮助提示 */}
      <div className="text-sm text-left text-gray-500 space-y-3 p-4 bg-gray-50 rounded-lg border">
        <p className="font-medium text-gray-700">{t('forgotPassword.didntReceiveEmail')}</p>
        <ul className="list-disc list-inside space-y-2">
          <li>{t('forgotPassword.checkSpamFolder')}</li>
          <li>{t('forgotPassword.correctEmail')}</li>
          <li>{t('forgotPassword.waitFewMinutes')}</li>
          <li>{t('forgotPassword.tryResending')}</li>
        </ul>
      </div>

      {/* 其他操作 */}
      <div className="flex items-center justify-center gap-6 text-sm font-medium">
        <Link
          to="#"
          onClick={handleStartOver}
          className="flex items-center gap-2 text-purple-600 hover:text-purple-700 transition-colors"
        >
          <Mail className="w-4 h-4" />
          {t('forgotPassword.useDifferentEmail')}
        </Link>
        <Link
          to="/login"
          className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          {t('forgotPassword.backToLogin')}
        </Link>
      </div>
    </motion.div>
  );

  return (
    <EnhancedAuthLayout
      title={t('forgotPassword.title')}
      subtitle={t('forgotPassword.subtitle')}
      description={t('forgotPassword.subtitle')}
      showBackButton={!state.emailSent}
      backButtonText={t('forgotPassword.backToLogin')}
      backButtonHref="/login"
      variant="default"
    >
      <AnimatePresence mode="wait">
        {!state.emailSent ? (
          <EmailInputView key="emailInput" />
        ) : (
          <SuccessView key="success" />
        )}
      </AnimatePresence>
    </EnhancedAuthLayout>
  );
}
