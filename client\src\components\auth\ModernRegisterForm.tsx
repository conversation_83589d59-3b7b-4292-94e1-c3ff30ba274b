import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import { ModernInput } from './ModernInput';
import { ModernButton } from './ModernButton';
import { Mail, Lock, User, Chrome, CheckCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  terms: boolean;
}

const passwordStrengthCheck = (password: string) => {
  const checks = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    number: /[0-9]/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  };
  
  const score = Object.values(checks).filter(Boolean).length;
  return { checks, score };
};

export function ModernRegisterForm() {
  const { signUp, signInWithGoogle } = useAuth();
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    terms: false
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showPasswordStrength, setShowPasswordStrength] = useState(false);

  const passwordStrength = passwordStrengthCheck(formData.password);

  const validateStep1 = () => {
    const newErrors: {[key: string]: string} = {};
    
    if (!formData.firstName.trim()) {
      newErrors.firstName = '请输入名字';
    }
    
    if (!formData.lastName.trim()) {
      newErrors.lastName = '请输入姓氏';
    }
    
    if (!formData.email) {
      newErrors.email = '请输入邮箱地址';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = () => {
    const newErrors: {[key: string]: string} = {};
    
    if (!formData.password) {
      newErrors.password = '请输入密码';
    } else if (passwordStrength.score < 3) {
      newErrors.password = '密码强度不够，请选择更强的密码';
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认密码';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = '密码不匹配';
    }
    
    if (!formData.terms) {
      newErrors.terms = '请同意用户协议和隐私政策';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextStep = () => {
    if (step === 1 && validateStep1()) {
      setStep(2);
    }
  };

  const handlePrevStep = () => {
    if (step === 2) {
      setStep(1);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (step === 1) {
      handleNextStep();
      return;
    }
    
    if (!validateStep2()) return;
    
    setLoading(true);
    setErrors({});
    
    try {
      const { success, error } = await signUp(
        formData.email,
        formData.password,
        {
          firstName: formData.firstName,
          lastName: formData.lastName
        }
      );
      
      if (!success) {
        toast.error(error || '注册失败');
        setErrors({ general: error || '注册失败' });
      } else {
        toast.success('注册成功！请查收邮件进行验证。');
      }
    } catch (error) {
      console.error('Register error:', error);
      toast.error('注册过程中发生错误');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignup = async () => {
    setGoogleLoading(true);
    try {
      await signInWithGoogle();
    } catch (error) {
      console.error('Google signup error:', error);
      toast.error('Google注册失败');
    } finally {
      setGoogleLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = field === 'terms' ? e.target.checked : e.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    
    // 显示密码强度检查
    if (field === 'password') {
      setShowPasswordStrength(true);
    }
  };

  const getPasswordStrengthColor = () => {
    if (passwordStrength.score <= 2) return 'bg-red-500';
    if (passwordStrength.score <= 3) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getPasswordStrengthText = () => {
    if (passwordStrength.score <= 2) return '弱';
    if (passwordStrength.score <= 3) return '中';
    return '强';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* 进度指示器 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
            step >= 1 ? 'bg-purple-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
          }`}>
            {step > 1 ? <CheckCircle className="w-5 h-5" /> : '1'}
          </div>
          <div className={`h-1 w-16 ${step >= 2 ? 'bg-purple-600' : 'bg-gray-200 dark:bg-gray-700'}`}></div>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
            step >= 2 ? 'bg-purple-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
          }`}>
            2
          </div>
        </div>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {step}/2
        </div>
      </div>

      {/* 通用错误信息 */}
      {errors.general && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <p className="text-sm text-red-600 dark:text-red-400">{errors.general}</p>
        </motion.div>
      )}

      <form onSubmit={handleSubmit} className="space-y-5">
        <AnimatePresence mode="wait">
          {step === 1 ? (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-5"
            >
              <div className="grid grid-cols-2 gap-4">
                <ModernInput
                  label="名字"
                  type="text"
                  value={formData.firstName}
                  onChange={handleInputChange('firstName')}
                  error={errors.firstName}
                  autoComplete="given-name"
                />
                <ModernInput
                  label="姓氏"
                  type="text"
                  value={formData.lastName}
                  onChange={handleInputChange('lastName')}
                  error={errors.lastName}
                  autoComplete="family-name"
                />
              </div>

              <ModernInput
                label="邮箱地址"
                type="email"
                value={formData.email}
                onChange={handleInputChange('email')}
                error={errors.email}
                leftIcon={<Mail className="w-5 h-5" />}
                autoComplete="email"
              />
            </motion.div>
          ) : (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-5"
            >
              <div className="space-y-2">
                <ModernInput
                  label="密码"
                  type="password"
                  value={formData.password}
                  onChange={handleInputChange('password')}
                  error={errors.password}
                  leftIcon={<Lock className="w-5 h-5" />}
                  showPasswordToggle
                  autoComplete="new-password"
                />
                
                {/* 密码强度指示器 */}
                {showPasswordStrength && formData.password && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="space-y-2"
                  >
                    <div className="flex items-center space-x-2">
                      <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`}
                          style={{ width: `${(passwordStrength.score / 5) * 100}%` }}
                        />
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {getPasswordStrengthText()}
                      </span>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className={`w-3 h-3 ${passwordStrength.checks.length ? 'text-green-500' : 'text-gray-300'}`} />
                        <span>至少8个字符</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className={`w-3 h-3 ${passwordStrength.checks.uppercase ? 'text-green-500' : 'text-gray-300'}`} />
                        <span>包含大写字母</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className={`w-3 h-3 ${passwordStrength.checks.number ? 'text-green-500' : 'text-gray-300'}`} />
                        <span>包含数字</span>
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>

              <ModernInput
                label="确认密码"
                type="password"
                value={formData.confirmPassword}
                onChange={handleInputChange('confirmPassword')}
                error={errors.confirmPassword}
                leftIcon={<Lock className="w-5 h-5" />}
                autoComplete="new-password"
              />

              <div className="space-y-2">
                <label className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.terms}
                    onChange={handleInputChange('terms')}
                    className="mt-1 rounded border-gray-300 text-purple-600 focus:ring-purple-500 focus:ring-offset-0"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    我同意{' '}
                    <Link to="/terms" className="text-purple-600 dark:text-purple-400 hover:underline">
                      用户协议
                    </Link>
                    {' '}和{' '}
                    <Link to="/privacy" className="text-purple-600 dark:text-purple-400 hover:underline">
                      隐私政策
                    </Link>
                  </span>
                </label>
                {errors.terms && (
                  <p className="text-sm text-red-600 dark:text-red-400">{errors.terms}</p>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <div className="flex space-x-4">
          {step === 2 && (
            <ModernButton
              type="button"
              variant="outline"
              size="lg"
              className="flex-1"
              onClick={handlePrevStep}
            >
              上一步
            </ModernButton>
          )}
          <ModernButton
            type="submit"
            loading={loading}
            size="lg"
            className="flex-1"
          >
            {step === 1 ? '下一步' : (loading ? '注册中...' : '创建账户')}
          </ModernButton>
        </div>
      </form>

      {step === 1 && (
        <>
          {/* 分割线 */}
          <div className="flex items-center">
            <div className="flex-1 border-t border-gray-200 dark:border-gray-700"></div>
            <span className="px-4 text-sm text-gray-500 dark:text-gray-400">或</span>
            <div className="flex-1 border-t border-gray-200 dark:border-gray-700"></div>
          </div>

          {/* Google注册 */}
          <ModernButton
            variant="outline"
            size="lg"
            className="w-full"
            loading={googleLoading}
            leftIcon={!googleLoading && <Chrome className="w-5 h-5" />}
            onClick={handleGoogleSignup}
          >
            {googleLoading ? '连接中...' : '使用 Google 注册'}
          </ModernButton>
        </>
      )}

      {/* 登录链接 */}
      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-300">
          已有账户？{' '}
          <Link
            to="/login"
            className="text-purple-600 dark:text-purple-400 hover:text-purple-500 dark:hover:text-purple-300 font-semibold transition-colors"
          >
            立即登录
          </Link>
        </p>
      </div>
    </motion.div>
  );
} 