import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import { CustomerReviews } from '@/components/ui/reviews/CustomerReviews'
import UltimateCtaFooterMinimal from '@/components/enhanced-landing/UltimateCtaFooterMinimal'
import { HeroSection } from '@/components/ui/hero-section-dark'
import { FeaturesSectionWithHoverEffects } from '@/components/ui/feature-section-with-hover-effects'
import { AdaptiveNavigation } from '@/components/ui/adaptive-navigation'
import { Pricing } from '@/components/ui/pricing'
import EnhancedBackgroundPaths from '@/components/ui/modern-background-paths'
import { IntegrationsSection } from '@/components/ui/platform-integrations-cloud'
import { Home, User, Briefcase, FileText, Phone, Globe } from 'lucide-react'
import '@/styles/notfound-animations.css'
import { RainbowButton } from "@/components/ui/rainbow-button";
import { useTranslation } from 'react-i18next';
import { TFunction } from 'i18next';

// 导航项目
const navItems = (t: TFunction) => [
  { name: t('homePage.nav.home'), url: '#hero', icon: Home },
  { name: t('homePage.nav.features'), url: '#features', icon: Briefcase },
  { name: t('homePage.nav.pricing'), url: '#pricing', icon: FileText },
  { name: t('homePage.nav.about'), url: '#testimonials', icon: User },
  { name: t('homePage.nav.contact'), url: '#cta', icon: Phone }
];

// 语言选项
const availableLanguages = [
  { code: 'en', name: 'English' },
  { code: 'zh', name: '中文' },
  { code: 'ms', name: 'Bahasa Melayu' }
];

// iTeraBiz 定价方案
const iTeraBizPlans = (t: TFunction) => [
  {
    name: t('homePage.pricing.plans.starter.name'),
    price: t('homePage.pricing.plans.starter.price'),
    yearlyPrice: t('homePage.pricing.plans.starter.yearlyPrice'),
    period: t('homePage.pricing.plans.starter.period'),
    features: t('homePage.pricing.plans.starter.features', { returnObjects: true }) as string[],
    description: t('homePage.pricing.plans.starter.description'),
    buttonText: t('homePage.pricing.plans.starter.buttonText'),
    href: "/register",
    isPopular: false,
  },
  {
    name: t('homePage.pricing.plans.professional.name'),
    price: t('homePage.pricing.plans.professional.price'),
    yearlyPrice: t('homePage.pricing.plans.professional.yearlyPrice'),
    period: t('homePage.pricing.plans.professional.period'),
    features: t('homePage.pricing.plans.professional.features', { returnObjects: true }) as string[],
    description: t('homePage.pricing.plans.professional.description'),
    buttonText: t('homePage.pricing.plans.professional.buttonText'),
    href: "/register",
    isPopular: true,
  },
  {
    name: t('homePage.pricing.plans.enterprise.name'),
    price: t('homePage.pricing.plans.enterprise.price'),
    yearlyPrice: t('homePage.pricing.plans.enterprise.yearlyPrice'),
    period: t('homePage.pricing.plans.enterprise.period'),
    features: t('homePage.pricing.plans.enterprise.features', { returnObjects: true }) as string[],
    description: t('homePage.pricing.plans.enterprise.description'),
    buttonText: t('homePage.pricing.plans.enterprise.buttonText'),
    href: "/contact",
    isPopular: false,
  },
];

export default function HomePage() {
  const { i18n, t } = useTranslation();
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // Check if dark mode is already enabled
    return document.documentElement.classList.contains('dark');
  });
  const navigate = useNavigate();

  const handleDarkModeToggle = (darkMode: boolean) => {
    setIsDarkMode(darkMode);
    // 修复：添加DOM操作以切换Tailwind的dark class
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  const handleLanguageChange = (langCode: string) => {
    i18n.changeLanguage(langCode);
  };

  return (
    <div className="min-h-screen bg-gradient-hero dark:bg-neutral-900">
      <Helmet>
        <title>{t('homePage.hero.title')}</title>
        <meta name="description" content={t('homePage.hero.description')} />
      </Helmet>

      {/* 自适应导航 - 统一管理logo、导航和CTA按钮 */}
      <AdaptiveNavigation
        items={navItems(t)}
        isDarkMode={isDarkMode}
        onDarkModeToggle={handleDarkModeToggle}
        languages={availableLanguages}
        currentLanguage={i18n.language}
        onLanguageChange={handleLanguageChange}
        logo={{
          src: "/images/iterabiz-logo.png",
          alt: "iTeraBiz Logo",
          onClick: () => navigate('/')
        }}
        ctaButton={{
          text: t('homePage.pricing.plans.professional.buttonText'),
          onClick: () => navigate('/register')
        }}
      />

      {/* Modern Hero Section */}
      <section id="hero">
        <HeroSection
          title={t('homePage.hero.title')}
          subtitle={{
            regular: t('homePage.hero.subtitleRegular'),
            gradient: t('homePage.hero.subtitleGradient'),
          }}
          description={t('homePage.hero.description')}
          ctaText={t('homePage.hero.cta')}
          onCtaClick={() => navigate('/register')}
          gridOptions={{
            angle: 65,
            opacity: 0.4,
            cellSize: 50,
            lightLineColor: "#4a4a4a",
            darkLineColor: "#2a2a2a",
          }}
        />
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8 bg-white dark:bg-neutral-900">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-neutral-800 dark:text-neutral-100 mb-4">
              {t('homePage.features.title')}
            </h2>
            <p className="text-xl text-neutral-600 dark:text-neutral-300 max-w-2xl mx-auto">
              {t('homePage.features.description')}
            </p>
          </div>
          <FeaturesSectionWithHoverEffects />
          
          {/* Platform Integrations Section - move inside features section */}
          <div className="mt-20">
            <IntegrationsSection />
          </div>
        </div>
      </section>

      {/* Modern Pricing Section */}
      <section id="pricing" className="bg-neutral-50 dark:bg-neutral-800">
        <Pricing 
          plans={iTeraBizPlans(t).map(plan =>
            plan.buttonText === "Contact Sales"
              ? { ...plan, href: "/contact" }
              : plan
          )}
          title={t('homePage.pricing.title')}
          description={t('homePage.pricing.description')}
        />
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8 bg-white dark:bg-neutral-900">
        <div className="max-w-7xl mx-auto">
          <CustomerReviews />
        </div>
      </section>

      {/* Modern CTA Section with Dynamic Background (moved below testimonials) */}
      <section id="cta" className="relative">
        <EnhancedBackgroundPaths
          title={t('homePage.cta.title')}
        />
      </section>

      {/* Footer */}
      <UltimateCtaFooterMinimal />
    </div>
  );
}