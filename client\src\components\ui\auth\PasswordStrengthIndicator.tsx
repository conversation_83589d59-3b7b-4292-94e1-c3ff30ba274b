import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Check, X } from 'lucide-react';

interface PasswordStrengthIndicatorProps {
  password: string;
  className?: string;
}

interface PasswordCriteria {
  label: string;
  test: (password: string) => boolean;
}

const passwordCriteria: PasswordCriteria[] = [
  {
    label: 'At least 8 characters',
    test: (password) => password.length >= 8
  },
  {
    label: 'Contains uppercase letter',
    test: (password) => /[A-Z]/.test(password)
  },
  {
    label: 'Contains lowercase letter',
    test: (password) => /[a-z]/.test(password)
  },
  {
    label: 'Contains number',
    test: (password) => /\d/.test(password)
  },
  {
    label: 'Contains special character',
    test: (password) => /[!@#$%^&*(),.?":{}|<>]/.test(password)
  }
];

const getPasswordStrength = (password: string): {
  score: number;
  level: 'weak' | 'fair' | 'good' | 'strong';
  color: string;
} => {
  const passedCriteria = passwordCriteria.filter(criteria => criteria.test(password));
  const score = passedCriteria.length;
  
  if (score <= 1) return { score, level: 'weak', color: 'red' };
  if (score <= 2) return { score, level: 'fair', color: 'orange' };
  if (score <= 3) return { score, level: 'good', color: 'yellow' };
  return { score, level: 'strong', color: 'green' };
};

export function PasswordStrengthIndicator({ password, className }: PasswordStrengthIndicatorProps) {
  const strength = getPasswordStrength(password);
  const showIndicator = password.length > 0;

  if (!showIndicator) return null;

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3 }}
      className={cn("mt-3 space-y-3", className)}
    >
      {/* 强度条 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Password strength</span>
          <span className={cn(
            "text-sm font-semibold capitalize",
            strength.color === 'red' && "text-red-600",
            strength.color === 'orange' && "text-orange-600",
            strength.color === 'yellow' && "text-yellow-600",
            strength.color === 'green' && "text-green-600"
          )}>
            {strength.level}
          </span>
        </div>
        
        <div className="flex gap-1">
          {[1, 2, 3, 4, 5].map((level) => (
            <motion.div
              key={level}
              className={cn(
                "h-2 flex-1 rounded-full transition-colors duration-300",
                level <= strength.score
                  ? strength.color === 'red' ? "bg-red-500"
                  : strength.color === 'orange' ? "bg-orange-500"
                  : strength.color === 'yellow' ? "bg-yellow-500"
                  : strength.color === 'green' ? "bg-green-500"
                  : "bg-gray-500"
                  : "bg-gray-200"
              )}
              initial={{ scaleX: 0 }}
              animate={{ scaleX: level <= strength.score ? 1 : 0 }}
              transition={{ duration: 0.3, delay: level * 0.05 }}
              style={{ transformOrigin: 'left' }}
            />
          ))}
        </div>
      </div>

      {/* 要求列表 */}
      <div className="space-y-2">
        <span className="text-sm font-medium text-gray-700">Requirements:</span>
        <div className="grid grid-cols-1 gap-1">
          {passwordCriteria.map((criteria, index) => {
            const isPassed = criteria.test(password);
            
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
                className="flex items-center gap-2"
              >
                <motion.div
                  animate={{ 
                    scale: isPassed ? 1 : 0.8,
                    rotate: isPassed ? 0 : 0
                  }}
                  transition={{ duration: 0.2 }}
                >
                  {isPassed ? (
                    <Check className="w-4 h-4 text-green-500" />
                  ) : (
                    <X className="w-4 h-4 text-gray-400" />
                  )}
                </motion.div>
                <span className={cn(
                  "text-sm transition-colors duration-200",
                  isPassed ? "text-green-600" : "text-gray-500"
                )}>
                  {criteria.label}
                </span>
              </motion.div>
            );
          })}
        </div>
      </div>
    </motion.div>
  );
}
