"use client"

import React, { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { motion, AnimatePresence } from "framer-motion"
import { Menu as MenuIcon, X, LucideIcon, Sun, Moon, Globe } from "lucide-react"
import { NavBar } from "./tubelight-navbar"
import { MenuContainer, MenuItem } from "./fluid-menu"
import { RainbowButton } from "./rainbow-button"
import { cn } from "@/lib/utils"

interface NavItem {
  name: string
  url: string
  icon: LucideIcon
}

interface Language {
  code: string;
  name: string;
}

interface AdaptiveNavigationProps {
  items: NavItem[]
  isDarkMode?: boolean
  onDarkModeToggle?: (isDark: boolean) => void
  logo?: {
    src: string
    alt: string
    onClick?: () => void
  }
  ctaButton?: {
    text: string
    onClick: () => void
  }
  languages?: Language[]
  currentLanguage?: string
  onLanguageChange?: (langCode: string) => void
}

export function AdaptiveNavigation({
  items,
  isDarkMode = false,
  onDarkModeToggle,
  logo,
  ctaButton,
  languages,
  currentLanguage,
  onLanguageChange
}: AdaptiveNavigationProps) {
  const [isMobile, setIsMobile] = useState(false)
  const [isFluidMenuOpen, setIsFluidMenuOpen] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768) // md breakpoint
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // 处理导航点击
  const handleNavigation = (item: NavItem) => {
    if (item.url.startsWith('#')) {
      // 处理锚点链接
      const element = document.getElementById(item.url.slice(1))
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    } else {
      // 处理路由导航
      navigate(item.url)
    }
    // 关闭移动端菜单
    setIsFluidMenuOpen(false)
  }

  // 桌面端渲染
  const renderDesktopNavigation = () => (
    <>
      {/* 桌面端Logo */}
      {logo && (
        <div className="fixed top-6 left-4 md:left-16 z-[101] pointer-events-auto">
          <button
            onClick={logo.onClick}
            className="group flex items-center transition-transform duration-300 ease-out
              bg-transparent border-none shadow-none p-0 m-0
              focus:outline-none focus:ring-0 appearance-none"
            style={{
              lineHeight: 0,
              background: 'none',
              border: 'none',
              borderWidth: 0,
              borderStyle: 'none',
              boxShadow: 'none',
              padding: 0,
              margin: 0,
              appearance: 'none',
              WebkitAppearance: 'none',
              MozAppearance: 'none',
            }}
            aria-label="Home"
          >
            <img
              src={logo.src}
              alt={logo.alt}
              className="h-12 w-auto select-none transition-transform duration-300 ease-out group-hover:scale-110"
              style={{ display: 'block', border: 'none', boxShadow: 'none' }}
              draggable={false}
            />
          </button>
        </div>
      )}

      {/* 桌面端Get Started按钮 */}
      {ctaButton && (
        <div className="fixed top-6 right-4 md:right-6 z-[101]">
          <RainbowButton onClick={ctaButton.onClick}>
            {ctaButton.text}
          </RainbowButton>
        </div>
      )}

      {/* 桌面端导航 */}
      <NavBar 
        items={items}
        isDarkMode={isDarkMode}
        onDarkModeToggle={onDarkModeToggle}
        languages={languages}
        currentLanguage={currentLanguage}
        onLanguageChange={onLanguageChange}
      />
    </>
  )

  // 移动端渲染
  const renderMobileNavigation = () => (
    <>
      {/* 移动端顶部栏 - 重新布局 */}
      <div className="fixed top-0 left-0 right-0 z-[100] bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200/20 dark:border-gray-700/20">
        <div className="flex items-center justify-between px-4 py-3">
          {/* Logo */}
          {logo && (
            <button
              onClick={logo.onClick}
              className="flex items-center transition-transform duration-300 ease-out hover:scale-105"
              aria-label="Home"
            >
              <img
                src={logo.src}
                alt={logo.alt}
                className="h-8 w-auto select-none"
                draggable={false}
              />
            </button>
          )}

          <div className="flex items-center gap-4">
            {/* Fluid Menu - 移至顶部栏 */}
            <div className="relative z-[102]">
              <MenuContainer isExpanded={isFluidMenuOpen}>
                <MenuItem 
                  icon={
                    <div className="relative w-6 h-6">
                      <motion.div
                        className="absolute inset-0 flex items-center justify-center"
                        initial={false}
                        animate={{ 
                          rotate: isFluidMenuOpen ? 180 : 0,
                          scale: isFluidMenuOpen ? 0.8 : 1
                        }}
                        transition={{ duration: 0.3 }}
                      >
                        {isFluidMenuOpen ? (
                          <X size={20} strokeWidth={2} />
                        ) : (
                          <MenuIcon size={20} strokeWidth={2} />
                        )}
                      </motion.div>
                    </div>
                  }
                  onClick={() => setIsFluidMenuOpen(!isFluidMenuOpen)}
                />
                {items.map((item, index) => (
                  <MenuItem
                    key={item.name}
                    icon={<item.icon size={20} />}
                    onClick={() => handleNavigation(item)}
                  />
                ))}
                {/* 暗色模式切换 - 整合进菜单 */}
                {onDarkModeToggle && (
                  <MenuItem
                    icon={isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
                    onClick={() => {
                      onDarkModeToggle(!isDarkMode);
                      setIsFluidMenuOpen(false); // 关闭菜单
                    }}
                  />
                )}
                {/* 语言切换 - 整合进菜单 */}
                {languages && onLanguageChange && languages.map(lang => (
                  <MenuItem
                    key={lang.code}
                    icon={currentLanguage === lang.code ? <span className="text-sm font-bold">{lang.code.toUpperCase()}</span> : <span className="text-sm">{lang.code.toUpperCase()}</span>}
                    onClick={() => {
                      onLanguageChange(lang.code);
                      setIsFluidMenuOpen(false); // 关闭菜单
                    }}
                  />
                ))}
              </MenuContainer>
            </div>

            {/* Get Started按钮 */}
            {ctaButton && (
              <RainbowButton 
                onClick={ctaButton.onClick}
                className="text-sm px-4 py-2"
              >
                {ctaButton.text}
              </RainbowButton>
            )}
          </div>
        </div>
      </div>
    </>
  )

  return (
    <div className="adaptive-navigation">
      {isMobile ? renderMobileNavigation() : renderDesktopNavigation()}
    </div>
  )
}

// 导出快捷使用的Hook
export function useAdaptiveNavigation() {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  return { isMobile }
} 