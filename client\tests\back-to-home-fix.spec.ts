import { test, expect } from '@playwright/test';

test.describe('Back to Home Button Fix', () => {
  test('登录页面 - Back to Home按钮应该可以点击', async ({ page }) => {
    await page.goto('http://localhost:3003/login');
    await page.waitForLoadState('networkidle');
    
    // 检查Back to Home按钮是否存在
    const backButton = page.locator('a:has-text("Back to Home")');
    await expect(backButton).toBeVisible();
    
    // 使用force点击来绕过任何覆盖层问题
    await backButton.click({ force: true });
    await page.waitForLoadState('networkidle');
    
    // 验证是否导航到home页面
    await expect(page).toHaveURL(/.*\/home/<USER>
  });

  test('注册页面 - Back to Home按钮应该可以点击', async ({ page }) => {
    await page.goto('http://localhost:3003/register');
    await page.waitForLoadState('networkidle');
    
    // 检查Back to Home按钮是否存在
    const backButton = page.locator('a:has-text("Back to Home")');
    await expect(backButton).toBeVisible();
    
    // 使用force点击来绕过任何覆盖层问题
    await backButton.click({ force: true });
    await page.waitForLoadState('networkidle');
    
    // 验证是否导航到home页面
    await expect(page).toHaveURL(/.*\/home/<USER>
  });

  test('忘记密码页面 - Back to Home按钮应该可以点击', async ({ page }) => {
    await page.goto('http://localhost:3003/forgot-password');
    await page.waitForLoadState('networkidle');
    
    // 检查Back to Home按钮是否存在
    const backButton = page.locator('a:has-text("Back to Home")');
    await expect(backButton).toBeVisible();
    
    // 使用force点击来绕过任何覆盖层问题
    await backButton.click({ force: true });
    await page.waitForLoadState('networkidle');
    
    // 验证是否导航到home页面
    await expect(page).toHaveURL(/.*\/home/<USER>
  });

  test('404页面 - Back to Home按钮应该可以点击', async ({ page }) => {
    await page.goto('http://localhost:3003/nonexistent-page');
    await page.waitForLoadState('networkidle');
    
    // 检查Back to Home按钮是否存在
    const backButton = page.locator('a:has-text("Back to Home")');
    await expect(backButton).toBeVisible();
    
    // 使用force点击来绕过任何覆盖层问题
    await backButton.click({ force: true });
    await page.waitForLoadState('networkidle');
    
    // 验证是否导航到home页面
    await expect(page).toHaveURL(/.*\/home/<USER>
  });

  test('验证AuroraBackground不再阻挡点击事件', async ({ page }) => {
    await page.goto('http://localhost:3003/login');
    await page.waitForLoadState('networkidle');
    
    // 检查AuroraBackground的覆盖层是否有pointer-events-none
    const auroraOverlay = page.locator('.absolute.inset-0.overflow-hidden');
    await expect(auroraOverlay).toHaveClass(/pointer-events-none/);
  });
});
