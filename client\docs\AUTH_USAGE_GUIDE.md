# IteraBiz认证页面使用指南

## 🚀 快速开始

### 基本使用

```typescript
import { 
  EnhancedAuthLayout, 
  EnhancedAuthInput, 
  EnhancedAuthButton,
  GoogleSignInButton 
} from '@/components/ui/auth';

function MyAuthPage() {
  return (
    <EnhancedAuthLayout
      title="Welcome"
      subtitle="Sign in to continue"
      showLogo={true}
      showBackButton={true}
    >
      <form>
        <EnhancedAuthInput
          type="email"
          label="Email"
          placeholder="Enter your email"
          autoIcon={true}
        />
        
        <EnhancedAuthButton
          type="submit"
          variant="primary"
          fullWidth={true}
        >
          Sign In
        </EnhancedAuthButton>
        
        <GoogleSignInButton
          onGoogleSignIn={handleGoogleLogin}
        />
      </form>
    </EnhancedAuthLayout>
  );
}
```

## 🎨 组件详细使用

### EnhancedAuthLayout

主布局组件，提供统一的页面结构和样式。

```typescript
<EnhancedAuthLayout
  title="Create Account"                    // 页面标题
  subtitle="Join us today"                  // 副标题
  description="SEO描述"                     // 页面描述
  showLogo={true}                          // 显示Logo
  showBackButton={true}                    // 显示返回按钮
  backButtonText="← Back to Home"          // 返回按钮文字
  backButtonHref="/home"                   // 返回链接
  variant="default"                        // 样式变体
  className="custom-class"                 // 自定义样式
>
  {/* 页面内容 */}
</EnhancedAuthLayout>
```

**变体选项：**
- `default`：标准玻璃态效果
- `glass`：强化玻璃态效果
- `minimal`：极简白色背景

### EnhancedAuthInput

增强输入框组件，支持多种功能和样式。

```typescript
<EnhancedAuthInput
  type="email"                             // 输入类型
  label="Email Address"                    // 标签文字
  placeholder="Enter your email"           // 占位符
  value={email}                           // 输入值
  onChange={handleChange}                 // 变化回调
  error="Invalid email"                   // 错误信息
  success="Email verified"                // 成功信息
  hint="We'll never share your email"     // 提示信息
  leftIcon={<Mail />}                     // 左侧图标
  rightIcon={<Check />}                   // 右侧图标
  showPasswordToggle={true}               // 密码切换按钮
  variant="default"                       // 样式变体
  size="md"                              // 尺寸
  autoIcon={true}                        // 自动图标
  required                               // 必填
/>
```

**变体选项：**
- `default`：标准样式
- `floating`：浮动标签
- `minimal`：极简边框

**尺寸选项：**
- `sm`：小尺寸
- `md`：中等尺寸
- `lg`：大尺寸

### EnhancedAuthButton

增强按钮组件，支持多种样式和状态。

```typescript
<EnhancedAuthButton
  type="submit"                           // 按钮类型
  variant="primary"                       // 样式变体
  size="md"                              // 尺寸
  loading={isLoading}                    // 加载状态
  disabled={isDisabled}                  // 禁用状态
  fullWidth={true}                       // 全宽
  gradient={true}                        // 渐变效果
  icon={<ArrowRight />}                  // 图标
  iconPosition="left"                    // 图标位置
  onClick={handleClick}                  // 点击回调
>
  Sign In
</EnhancedAuthButton>
```

**变体选项：**
- `primary`：主要按钮（紫色渐变）
- `secondary`：次要按钮（灰色）
- `outline`：轮廓按钮（透明+边框）
- `ghost`：幽灵按钮（完全透明）
- `danger`：危险按钮（红色）

### GoogleSignInButton

Google原生设计登录按钮。

```typescript
<GoogleSignInButton
  loading={googleLoading}                 // 加载状态
  variant="default"                       // 样式变体
  size="md"                              // 尺寸
  fullWidth={true}                       // 全宽
  onGoogleSignIn={handleGoogleLogin}      // Google登录回调
  disabled={isDisabled}                  // 禁用状态
>
  Continue with Google
</GoogleSignInButton>
```

### PasswordStrengthIndicator

密码强度指示器组件。

```typescript
<PasswordStrengthIndicator
  password={password}                     // 密码值
  className="mt-3"                       // 自定义样式
/>
```

## 🌐 国际化使用

### 基本用法

```typescript
import { useTranslation } from '@/i18n/useTranslation';

function LoginPage() {
  const { t, currentLanguage, setLanguage } = useTranslation();
  
  return (
    <div>
      <h1>{t('login.title')}</h1>
      <p>{t('login.subtitle')}</p>
      
      {/* 语言切换 */}
      <select 
        value={currentLanguage} 
        onChange={(e) => setLanguage(e.target.value)}
      >
        <option value="en">English</option>
        <option value="zh">中文</option>
        <option value="ms">Bahasa Melayu</option>
      </select>
    </div>
  );
}
```

### 翻译键值

```typescript
// 常用翻译键
t('common.email')           // "Email"
t('common.password')        // "Password"
t('login.title')           // "Welcome Back"
t('login.signInButton')    // "Sign In"
t('register.title')        // "Create Your Account"
t('errors.required')       // "This field is required"
t('errors.invalidEmail')   // "Please enter a valid email"
```

## 🎭 主题切换

### 深色主题

```typescript
// 自动检测系统主题
const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

// 手动切换主题
function toggleTheme() {
  document.documentElement.classList.toggle('dark');
}

// 监听主题变化
window.matchMedia('(prefers-color-scheme: dark)')
  .addEventListener('change', (e) => {
    if (e.matches) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  });
```

## 📱 响应式设计

### 断点使用

```typescript
// Tailwind CSS响应式类名
<div className="
  w-full                    // 默认全宽
  sm:w-auto                // 小屏幕自动宽度
  md:max-w-md              // 中等屏幕最大宽度
  lg:max-w-lg              // 大屏幕最大宽度
  px-4 sm:px-6 lg:px-8     // 响应式内边距
">
  内容
</div>
```

### 移动端优化

```typescript
// 触摸友好的按钮尺寸
<EnhancedAuthButton
  size="md"                // 确保最小44px触摸目标
  className="min-h-[44px]" // 明确设置最小高度
>
  按钮
</EnhancedAuthButton>

// 移动端输入框优化
<EnhancedAuthInput
  type="email"
  autoComplete="email"     // 自动完成
  autoCapitalize="none"    // 禁用自动大写
  autoCorrect="off"        // 禁用自动纠错
/>
```

## 🔧 自定义样式

### CSS变量

```css
:root {
  --auth-primary: #9F7AEA;
  --auth-primary-hover: #8B5CF6;
  --auth-border-radius: 0.75rem;
  --auth-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark {
  --auth-bg: #0f172a;
  --auth-text: #f8fafc;
  --auth-border: #475569;
}
```

### 自定义组件

```typescript
// 扩展现有组件
const CustomAuthButton = styled(EnhancedAuthButton)`
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  
  &:hover {
    transform: translateY(-2px);
  }
`;

// 使用Tailwind自定义
<EnhancedAuthButton
  className="
    bg-gradient-to-r from-pink-500 to-violet-500
    hover:from-pink-600 hover:to-violet-600
    transform hover:-translate-y-1
    transition-all duration-300
  "
>
  自定义按钮
</EnhancedAuthButton>
```

## 🚨 错误处理

### 表单验证

```typescript
const [errors, setErrors] = useState({});

const validateForm = () => {
  const newErrors = {};
  
  if (!email) {
    newErrors.email = t('errors.required');
  } else if (!isValidEmail(email)) {
    newErrors.email = t('errors.invalidEmail');
  }
  
  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
};

// 在输入框中显示错误
<EnhancedAuthInput
  type="email"
  value={email}
  error={errors.email}
  onChange={(e) => {
    setEmail(e.target.value);
    // 清除错误
    if (errors.email) {
      setErrors(prev => ({ ...prev, email: undefined }));
    }
  }}
/>
```

### 异步错误处理

```typescript
const handleSubmit = async (e) => {
  e.preventDefault();
  
  try {
    setLoading(true);
    await authService.login(email, password);
    toast.success(t('success.loginSuccess'));
  } catch (error) {
    if (error.code === 'INVALID_CREDENTIALS') {
      setErrors({ general: t('errors.invalidCredentials') });
    } else {
      setErrors({ general: t('errors.generalError') });
    }
    toast.error(error.message);
  } finally {
    setLoading(false);
  }
};
```

## 🔍 调试和性能

### 性能监控

```typescript
// 启用性能监控（开发环境）
<PerformanceMonitor 
  enabled={process.env.NODE_ENV === 'development'}
  showMetrics={true}
  onMetricsUpdate={(metrics) => {
    console.log('Performance:', metrics);
  }}
/>

// 使用性能分析Hook
const { metrics, addMetrics, getPerformanceScore } = usePerformanceAnalysis();
```

### 调试技巧

```typescript
// 开发环境调试信息
if (process.env.NODE_ENV === 'development') {
  console.log('Auth State:', authState);
  console.log('Form Data:', formData);
  console.log('Errors:', errors);
}

// 性能监控快捷键
// 按 Ctrl+Shift+P 显示性能指标
```

## 📚 最佳实践

### 代码组织

```typescript
// 推荐的文件结构
components/
├── forms/
│   ├── LoginForm.tsx
│   ├── RegisterForm.tsx
│   └── ForgotPasswordForm.tsx
├── ui/
│   └── auth/
│       ├── EnhancedAuthLayout.tsx
│       ├── EnhancedAuthInput.tsx
│       └── EnhancedAuthButton.tsx
└── hooks/
    ├── useAuthForm.ts
    └── usePasswordValidation.ts
```

### 性能优化

```typescript
// 使用React.memo防止不必要的重渲染
const OptimizedComponent = React.memo(MyComponent);

// 使用useCallback优化事件处理
const handleSubmit = useCallback(async (data) => {
  // 处理逻辑
}, [dependencies]);

// 懒加载页面组件
const LoginPage = lazy(() => import('./pages/LoginPage'));
```

### 可访问性

```typescript
// 确保可访问性
<EnhancedAuthInput
  id="email"
  label="Email Address"
  aria-describedby="email-error"
  aria-invalid={!!errors.email}
  required
/>

{errors.email && (
  <div id="email-error" role="alert">
    {errors.email}
  </div>
)}
```

---

## 🆘 常见问题

**Q: 如何自定义主题颜色？**
A: 修改 `auth-tokens.ts` 中的颜色定义，或使用CSS变量覆盖。

**Q: 如何添加新的语言支持？**
A: 在 `i18n/translations/` 中添加新的翻译文件，并更新配置。

**Q: 如何禁用性能监控？**
A: 设置环境变量 `REACT_APP_ENABLE_PERFORMANCE_MONITOR=false`。

**Q: 如何自定义验证规则？**
A: 扩展 `validation.ts` 文件中的验证函数。

---

**文档版本**：v2.0  
**最后更新**：2025年1月
