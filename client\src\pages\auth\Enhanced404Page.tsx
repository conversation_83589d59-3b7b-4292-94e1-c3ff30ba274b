import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { 
  EnhancedAuthLayout, 
  EnhancedAuthButton 
} from '../../components/ui/auth';
import { 
  Home, 
  ArrowLeft, 
  Search, 
  HelpCircle, 
  RefreshCw,
  AlertTriangle,
  Compass,
  Clock
} from 'lucide-react';
import { useTranslation } from '../../i18n/useTranslation';

interface CountdownState {
  seconds: number;
  isActive: boolean;
}

export default function Enhanced404Page() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [countdown, setCountdown] = useState<CountdownState>({
    seconds: 10,
    isActive: true
  });
  const [showSuggestions, setShowSuggestions] = useState(false);

  // 倒计时逻辑
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    
    if (countdown.isActive && countdown.seconds > 0) {
      interval = setInterval(() => {
        setCountdown(prev => ({
          ...prev,
          seconds: prev.seconds - 1
        }));
      }, 1000);
    } else if (countdown.seconds === 0 && countdown.isActive) {
      navigate('/home');
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [countdown.isActive, countdown.seconds, navigate]);

  // 停止倒计时
  const stopCountdown = () => {
    setCountdown(prev => ({ ...prev, isActive: false }));
  };

  // 手动导航
  const handleNavigation = (path: string) => {
    stopCountdown();
    navigate(path);
  };

  // 刷新页面
  const handleRefresh = () => {
    window.location.reload();
  };

  // 显示建议
  const toggleSuggestions = () => {
    setShowSuggestions(!showSuggestions);
  };

  // 3D动画变体
  const floatingVariants: Variants = {
    initial: { y: 0, rotateX: 0, rotateY: 0 },
    animate: {
      y: [-10, 10, -10],
      rotateX: [-5, 5, -5],
      rotateY: [-5, 5, -5],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  // 粒子动画
  const particleVariants = {
    initial: { opacity: 0, scale: 0 },
    animate: {
      opacity: [0, 1, 0],
      scale: [0, 1, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        delay: Math.random() * 2
      }
    }
  };

  return (
    <EnhancedAuthLayout
      title={t('notFound.title')}
      subtitle={t('notFound.subtitle')}
      description={t('notFound.title')}
      showLogo={true}
      showBackButton={true}
      backButtonText={t('notFound.backToHome')}
      backButtonHref="/home"
      variant="default"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center space-y-8"
      >
        {/* 3D 404 动画 */}
        <div className="relative">
          <motion.div
            variants={floatingVariants}
            initial="initial"
            animate="animate"
            className="relative"
          >
            {/* 主要404文字 */}
            <div className="text-6xl sm:text-8xl md:text-9xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-600 via-purple-700 to-purple-800 select-none">
              404
            </div>

            {/* 3D阴影效果 */}
            <div className="absolute inset-0 text-6xl sm:text-8xl md:text-9xl font-bold text-purple-200 transform translate-x-1 translate-y-1 sm:translate-x-2 sm:translate-y-2 -z-10 select-none">
              404
            </div>
          </motion.div>

          {/* 浮动粒子 */}
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(8)].map((_, i) => (
              <motion.div
                key={i}
                variants={particleVariants}
                initial="initial"
                animate="animate"
                className="absolute w-2 h-2 bg-purple-400 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
              />
            ))}
          </div>
        </div>

        {/* 错误信息 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="space-y-4"
        >
          <div className="flex items-center justify-center gap-2 text-orange-600">
            <AlertTriangle className="w-5 h-5" />
            <span className="font-medium">{t('notFound.lostInSpace')}</span>
          </div>
          
          <p className="text-gray-600 max-w-md mx-auto">
            {t('notFound.pageNotFoundDetails')}
          </p>
        </motion.div>

        {/* 自动重定向倒计时 */}
        <AnimatePresence>
          {countdown.isActive && countdown.seconds > 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3 }}
              className="p-4 bg-blue-50 border border-blue-200 rounded-xl"
            >
              <div className="flex items-center justify-center gap-3">
                <Clock className="w-5 h-5 text-blue-600" />
                <span className="text-blue-800">
                  {t('notFound.redirectingToHome')}{' '}
                  <span className="font-bold text-blue-900">{countdown.seconds}</span>{' '}
                  {t('common.seconds').toLowerCase()}
                </span>
                <button
                  onClick={stopCountdown}
                  className="text-blue-600 hover:text-blue-800 text-sm underline"
                >
                  {t('common.cancel')}
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 操作按钮 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          className="space-y-4"
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
            <EnhancedAuthButton
              variant="primary"
              size="md"
              fullWidth={true}
              gradient={true}
              icon={<Home className="w-4 h-4" />}
              onClick={() => handleNavigation('/home')}
            >
              {t('notFound.goHome')}
            </EnhancedAuthButton>

            <EnhancedAuthButton
              variant="outline"
              size="md"
              fullWidth={true}
              icon={<ArrowLeft className="w-4 h-4" />}
              onClick={() => window.history.back()}
            >
              {t('notFound.goBack')}
            </EnhancedAuthButton>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
            <EnhancedAuthButton
              variant="ghost"
              size="md"
              fullWidth={true}
              icon={<RefreshCw className="w-4 h-4" />}
              onClick={handleRefresh}
            >
              {t('common.refreshPage')}
            </EnhancedAuthButton>
            
            <EnhancedAuthButton
              variant="ghost"
              size="md"
              fullWidth={true}
              icon={<HelpCircle className="w-4 h-4" />}
              onClick={toggleSuggestions}
            >
              {t('common.getHelp')}
            </EnhancedAuthButton>
          </div>
        </motion.div>

        {/* 帮助建议 */}
        <AnimatePresence>
          {showSuggestions && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.4 }}
              className="mt-8 p-6 bg-gray-50/80 border border-gray-200/80 rounded-2xl text-left"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <Compass className="w-6 h-6 text-purple-600" />
                  <h3 className="text-lg font-semibold text-gray-900">{t('notFound.suggestionsTitle')}</h3>
                </div>
                <button 
                  onClick={toggleSuggestions} 
                  className="text-gray-400 hover:text-gray-600"
                  aria-label={t('notFound.closeSuggestions')}
                >
                  &times;
                </button>
              </div>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start gap-3">
                  <span className="text-purple-500 mt-1">&rarr;</span>
                  <span>
                    {t('notFound.checkURLSuggestion', {
                      refreshLink: (
                        <button onClick={handleRefresh} className="text-purple-600 hover:underline">
                          {t('common.refreshPage').toLowerCase()}
                        </button>
                      )
                    })}
                  </span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-purple-500 mt-1">&rarr;</span>
                  <span>
                    {t('notFound.returnToHomeSuggestion', {
                      homeLink: (
                        <Link to="/home" onClick={() => stopCountdown()} className="text-purple-600 hover:underline">
                          {t('notFound.homepage')}
                        </Link>
                      )
                    })}
                  </span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-purple-500 mt-1">&rarr;</span>
                  <span>
                    {t('notFound.useSearch')}
                  </span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-purple-500 mt-1">&rarr;</span>
                  <span>
                    {t('notFound.contactSupportSuggestion', {
                      contactLink: (
                        <Link to="/contact" className="text-purple-600 hover:underline">
                          {t('common.contactSupport')}
                        </Link>
                      )
                    })}
                  </span>
                </li>
              </ul>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 快速链接 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          className="pt-8 border-t border-gray-200"
        >
          <p className="text-sm text-gray-500 mb-4">{t('notFound.popularPagesTitle')}</p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link 
              to="/home" 
              className="text-purple-600 hover:text-purple-700 transition-colors"
              onClick={stopCountdown}
            >
              {t('notFound.homeLink')}
            </Link>
            <Link 
              to="/login" 
              className="text-purple-600 hover:text-purple-700 transition-colors"
              onClick={stopCountdown}
            >
              {t('notFound.loginLink')}
            </Link>
            <Link 
              to="/register" 
              className="text-purple-600 hover:text-purple-700 transition-colors"
              onClick={stopCountdown}
            >
              {t('notFound.signUpLink')}
            </Link>
            <Link 
              to="/contact" 
              className="text-purple-600 hover:text-purple-700 transition-colors"
              onClick={stopCountdown}
            >
              {t('notFound.contactLink')}
            </Link>
          </div>
        </motion.div>
      </motion.div>
    </EnhancedAuthLayout>
  );
}
