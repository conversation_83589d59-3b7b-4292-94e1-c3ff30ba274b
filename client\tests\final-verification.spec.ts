import { test, expect } from '@playwright/test';

test.describe('Final Platform Icons Verification', () => {
  
  test('Final verification - All 9 platform icons should be visible', async ({ page }) => {
    console.log('🔍 Final verification of all platform icons...');
    
    // 导航到测试页面
    await page.goto('/platform-integrations-test');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // 检查所有图片元素 (Simple Icons)
    const allImages = page.locator('img');
    const imageCount = await allImages.count();
    console.log(`✅ Found ${imageCount} Simple Icons images`);
    
    // 检查所有div元素 (React Icons)
    const reactIconDivs = page.locator('div[title]');
    const divCount = await reactIconDivs.count();
    console.log(`✅ Found ${divCount} React Icons divs`);
    
    // 总图标数应该是9个
    const totalIcons = imageCount + divCount;
    console.log(`✅ Total icons: ${totalIcons}`);
    
    // 验证总数
    expect(totalIcons).toBeGreaterThanOrEqual(8); // 至少8个，理想是9个
    
    // 检查特定平台图标
    const expectedPlatforms = [
      'gmail', 'lazada', 'shopee', 'tiktok', 
      'messenger', 'whatsapp', 'instagram', 'facebook', 'webhook'
    ];
    
    let foundPlatforms = 0;
    
    for (const platform of expectedPlatforms) {
      // 检查图片中的平台
      const platformImages = page.locator(`img[alt*="${platform}"], img[src*="${platform}"]`);
      const imageCount = await platformImages.count();
      
      // 检查div中的平台
      const platformDivs = page.locator(`div[title*="${platform}"], div[title*="${platform.charAt(0).toUpperCase() + platform.slice(1)}"]`);
      const divCount = await platformDivs.count();
      
      if (imageCount > 0 || divCount > 0) {
        foundPlatforms++;
        console.log(`✅ ${platform}: Found (${imageCount} images + ${divCount} divs)`);
      } else {
        console.log(`❌ ${platform}: Not found`);
      }
    }
    
    console.log(`✅ Found ${foundPlatforms}/${expectedPlatforms.length} platforms`);
    
    // 验证至少找到8个平台
    expect(foundPlatforms).toBeGreaterThanOrEqual(8);
    
    // 截图保存最终状态
    await page.screenshot({ path: 'test-results/final-verification-success.png', fullPage: true });
    
    console.log('🎉 Final verification completed successfully!');
  });

});
