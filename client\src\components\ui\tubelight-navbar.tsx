"use client"

import React, { useEffect, useState } from "react"
import { motion } from "framer-motion"
import { useNavigate } from "react-router-dom"
import { LucideIcon, Moon, Sun, Globe } from "lucide-react"
import { cn } from "@/lib/utils"
import { 
  DropdownMenu, 
  DropdownMenuTrigger, 
  DropdownMenuContent, 
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator
} from './dropdown-menu'
import { Button } from './button'

interface NavItem {
  name: string
  url: string
  icon: LucideIcon
}

interface Language {
  code: string;
  name: string;
}

interface NavBarProps {
  items: NavItem[]
  className?: string
  onDarkModeToggle?: (isDark: boolean) => void
  isDarkMode?: boolean
  languages?: Language[]
  currentLanguage?: string
  onLanguageChange?: (langCode: string) => void
}

export function NavBar({ 
  items, 
  className,
  onDarkModeToggle,
  isDarkMode = false,
  languages,
  currentLanguage,
  onLanguageChange
}: NavBarProps) {
  const [activeTab, setActiveTab] = useState(items[0].name)
  const [isMobile, setIsMobile] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // 滚动检测功能
  useEffect(() => {
    const handleScroll = () => {
      const sections = items.filter(item => item.url.startsWith('#'))
      let currentSection = sections[0]?.name || items[0].name

      for (const item of sections) {
        const element = document.getElementById(item.url.slice(1))
        if (element) {
          const rect = element.getBoundingClientRect()
          // 如果section在视口中间位置，设为active
          if (rect.top <= window.innerHeight / 2 && rect.bottom >= window.innerHeight / 2) {
            currentSection = item.name
            break
          }
          // 如果section在视口上半部分，也设为active
          if (rect.top <= 100 && rect.bottom > 100) {
            currentSection = item.name
          }
        }
      }

      setActiveTab(currentSection)
    }

    // 添加滚动监听
    window.addEventListener('scroll', handleScroll)
    // 初始检测
    handleScroll()

    return () => window.removeEventListener('scroll', handleScroll)
  }, [items])

  const handleNavigation = (item: NavItem) => {
    setActiveTab(item.name)
    if (item.url.startsWith('#')) {
      // Handle anchor links for same page
      const element = document.getElementById(item.url.slice(1))
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    } else {
      // Handle route navigation
      navigate(item.url)
    }
  }

  const toggleDarkMode = () => {
    const newDarkMode = !isDarkMode
    onDarkModeToggle?.(newDarkMode)
    
    // Toggle dark class on document
    if (newDarkMode) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  return (
    <div
      className={cn(
        "fixed top-16 md:top-6 w-full flex justify-center z-[100] pointer-events-none",
        className,
      )}
    >
      <div className="flex items-center gap-3 bg-white/10 dark:bg-gray-900/10 border border-gray-200/20 dark:border-gray-700/20 backdrop-blur-xl py-1 px-1 rounded-full shadow-xl pointer-events-auto"
        style={{
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
          boxShadow: '0 25px 50px -12px rgba(139, 92, 246, 0.1)'
        }}
      >
        {items.map((item) => {
          const Icon = item.icon
          const isActive = activeTab === item.name

          return (
            <button
              key={item.name}
              onClick={() => handleNavigation(item)}
              className={cn(
                "relative cursor-pointer text-sm font-semibold px-6 py-2 rounded-full transition-all duration-300 ease-out",
                "text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400",
                isActive && "text-purple-600 dark:text-purple-400",
                "hover:scale-105 active:scale-95"
              )}
            >
              <span className="hidden md:inline relative z-10">{item.name}</span>
              <span className="md:hidden relative z-10">
                <Icon size={18} strokeWidth={2.5} />
              </span>
              {isActive && (
                <motion.div
                  layoutId="lamp"
                  className="absolute inset-0 w-full rounded-full -z-10"
                  style={{
                    background: 'rgba(139, 92, 246, 0.1)'
                  }}
                  initial={false}
                  transition={{
                    type: "spring",
                    stiffness: 400,
                    damping: 25,
                  }}
                >
                  {/* 主题色亮点 - 精确居中在文字上方 */}
                  <div 
                    className="absolute w-8 h-1 rounded-full shadow-lg"
                    style={{
                      top: '-12px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      background: '#8b5cf6',
                      boxShadow: '0 0 20px rgba(139, 92, 246, 0.5)'
                    }}
                  >
                    {/* 外层光晕 - 更大范围的发光效果 */}
                    <div 
                      className="absolute w-12 h-3 rounded-full blur-md"
                      style={{
                        top: '-4px',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        background: 'rgba(139, 92, 246, 0.2)'
                      }}
                    />
                    {/* 中层光晕 - 中等范围的发光效果 */}
                    <div 
                      className="absolute w-8 h-2 rounded-full blur-sm"
                      style={{
                        top: '-2px',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        background: 'rgba(139, 92, 246, 0.4)'
                      }}
                    />
                    {/* 内层核心 - 最亮的核心发光 */}
                    <div 
                      className="absolute w-4 h-1 rounded-full blur-xs"
                      style={{
                        top: '0px',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        background: 'rgba(139, 92, 246, 0.6)'
                      }}
                    />
                  </div>
                </motion.div>
              )}
            </button>
          )
        })}
        
        {/* Dark Mode Toggle */}
        <div className="w-px h-6 bg-gray-200/30 dark:bg-gray-700/30 mx-1" />
        <button
          onClick={toggleDarkMode}
          className={cn(
            "relative cursor-pointer p-2.5 rounded-full transition-all duration-300 ease-out",
            "text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20",
            "hover:scale-105 active:scale-95"
          )}
          aria-label="Toggle dark mode"
        >
          {isDarkMode ? (
            <Sun size={16} strokeWidth={2.5} />
          ) : (
            <Moon size={16} strokeWidth={2.5} />
          )}
        </button>
        
        {/* Language Switcher */}
        {languages && onLanguageChange && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20">
                <Globe size={16} strokeWidth={2.5} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Select Language</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {languages.map(lang => (
                <DropdownMenuItem key={lang.code} onClick={() => onLanguageChange(lang.code)}>
                  {lang.name}
                  {currentLanguage === lang.code && <span className="ml-auto text-purple-600">✓</span>}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  )
} 