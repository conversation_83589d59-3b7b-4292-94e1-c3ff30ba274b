import React from 'react';
import { cn } from '../../../lib/utils';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'glass' | 'gradient';
  hover?: boolean;
  children: React.ReactNode
};

interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
};

interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
};

interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
};

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'default', hover = true, children, ...props }, ref) => {
    const baseStyles = [
      'rounded-lg',
      'transition-all duration-300 ease-out',
    ];

    const variants = {
      default: [
        'bg-white',
        'border border-border-default',
        'shadow-marketing-card',
      ],
      elevated: [
        'bg-white',
        'shadow-marketing-large',
        'border-0',
      ],
      outlined: [
        'bg-white',
        'border-2 border-primary-200',
        'shadow-marketing-sm',
      ],
      glass: [
        'glass-effect',
        'backdrop-blur-lg',
        'border border-white/20',
      ],
      gradient: [
        'bg-gradient-secondary',
        'border border-primary-100',
        'shadow-marketing-card',
      ],
    };

    const hoverEffects = hover
      ? [
          'hover:shadow-marketing-hover',
          'hover:-translate-y-1',
          'hover:scale-[1.02]',
          'cursor-pointer',
        ]
      : [];

    return (
      <div
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          hoverEffects,
          // className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('p-6 pb-4', 'border-b border-border-light', className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div ref={ref} className={cn('p-6', className)} {...props}>
        {children}
      </div>
    );
  }
);

const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'p-6 pt-4',
          'border-t border-border-light',
          'flex items-center justify-between',
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card'
CardHeader.displayName = 'CardHeader'
CardContent.displayName = 'CardContent'
CardFooter.displayName = 'CardFooter'

export { Card, CardHeader, CardContent, CardFooter }; 