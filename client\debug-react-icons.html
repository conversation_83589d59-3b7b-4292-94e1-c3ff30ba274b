<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Icons Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .icon-test {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .icon-box {
            width: 42px;
            height: 42px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f3f4f6;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>🔧 React Icons Debug Test</h1>
    
    <div class="test-container">
        <h2>Test 1: Direct React Icons Import</h2>
        <div id="reactIconsTest">
            <p>Loading React Icons...</p>
        </div>
    </div>

    <div class="test-container">
        <h2>Test 2: Platform Icons Configuration</h2>
        <div id="configTest">
            <p>Testing platform configuration...</p>
        </div>
    </div>

    <script type="module">
        // Test if we can load react-icons directly
        try {
            // This would normally be imported in a React environment
            console.log('Testing React Icons availability...');
            
            // Simulate the platform icons configuration
            const platformIcons = [
                { slug: "gmail", type: "simple" },
                { slug: "lazada", type: "react-icon", iconName: "FaShoppingCart" },
                { slug: "shopee", type: "simple" },
                { slug: "tiktok", type: "simple" },
                { slug: "messenger", type: "react-icon", iconName: "FaFacebookMessenger" },
                { slug: "whatsapp", type: "simple" },
                { slug: "instagram", type: "simple" },
                { slug: "facebook", type: "simple" },
                { slug: "webhook", type: "react-icon", iconName: "FaLink" },
            ];

            // Count different types
            const simpleIcons = platformIcons.filter(i => i.type === "simple");
            const reactIcons = platformIcons.filter(i => i.type === "react-icon");

            document.getElementById('configTest').innerHTML = `
                <div class="icon-test">
                    <strong>Total Icons:</strong> ${platformIcons.length}
                </div>
                <div class="icon-test">
                    <strong>Simple Icons:</strong> ${simpleIcons.length} (${simpleIcons.map(i => i.slug).join(', ')})
                </div>
                <div class="icon-test">
                    <strong>React Icons:</strong> ${reactIcons.length} (${reactIcons.map(i => i.slug).join(', ')})
                </div>
                <div class="icon-test">
                    <strong>React Icons Details:</strong>
                    <ul>
                        ${reactIcons.map(i => `<li>${i.slug}: ${i.iconName}</li>`).join('')}
                    </ul>
                </div>
            `;

            // Test Simple Icons URLs
            const simpleIconTests = simpleIcons.map(icon => {
                const url = `https://cdn.simpleicons.org/${icon.slug}`;
                return fetch(url)
                    .then(response => ({ icon: icon.slug, status: response.status, ok: response.ok }))
                    .catch(error => ({ icon: icon.slug, status: 'error', error: error.message }));
            });

            Promise.all(simpleIconTests).then(results => {
                const testResults = results.map(result => 
                    `<div class="icon-test">
                        <strong>${result.icon}:</strong> 
                        ${result.ok ? '✅ Available' : `❌ ${result.status} ${result.error || ''}`}
                    </div>`
                ).join('');

                document.getElementById('reactIconsTest').innerHTML = `
                    <h3>Simple Icons Availability Test:</h3>
                    ${testResults}
                `;
            });

        } catch (error) {
            document.getElementById('reactIconsTest').innerHTML = `
                <div class="icon-test">
                    <strong>Error:</strong> ${error.message}
                </div>
            `;
        }
    </script>
</body>
</html>
