import { test, expect } from '@playwright/test';

test('Quick icon visibility check', async ({ page }) => {
  console.log('🔍 Quick check for icon visibility...');
  
  await page.goto('/platform-integrations-test');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);
  
  // 检查所有图片的可见性
  const allImages = page.locator('img');
  const imageCount = await allImages.count();
  console.log(`Total images: ${imageCount}`);
  
  let visibleImages = 0;
  for (let i = 0; i < imageCount; i++) {
    const img = allImages.nth(i);
    const visible = await img.isVisible();
    const alt = await img.getAttribute('alt');
    if (visible) {
      visibleImages++;
      console.log(`✅ Image ${i} (${alt}) is VISIBLE`);
    } else {
      console.log(`❌ Image ${i} (${alt}) is HIDDEN`);
    }
  }
  
  // 检查所有div的可见性
  const allDivs = page.locator('div[title]');
  const divCount = await allDivs.count();
  console.log(`Total titled divs: ${divCount}`);
  
  let visibleDivs = 0;
  for (let i = 0; i < divCount; i++) {
    const div = allDivs.nth(i);
    const visible = await div.isVisible();
    const title = await div.getAttribute('title');
    if (visible) {
      visibleDivs++;
      console.log(`✅ Div ${i} (${title}) is VISIBLE`);
    } else {
      console.log(`❌ Div ${i} (${title}) is HIDDEN`);
    }
  }
  
  const totalVisible = visibleImages + visibleDivs;
  console.log(`\n📊 Summary: ${totalVisible} visible icons (${visibleImages} images + ${visibleDivs} divs)`);
  
  // 截图
  await page.screenshot({ path: 'test-results/quick-icon-check.png', fullPage: true });
  
  // 验证至少有一些图标可见
  expect(totalVisible).toBeGreaterThan(0);
});
