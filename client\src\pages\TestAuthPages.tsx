import React from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { AuroraBackground } from '../components/ui/aurora-background';
import { ModernButton } from '../components/auth/ModernButton';
import { Eye, Lock, Mail, User } from 'lucide-react';

export default function TestAuthPages() {
  return (
    <div className="min-h-screen">
      <Helmet>
        <title>认证页面测试 - iTeraBiz</title>
      </Helmet>

      <AuroraBackground className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              2025 现代认证页面
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              测试所有新的认证组件和页面
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* 登录页面 */}
            <Link
              to="/login"
              className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-white/30 dark:border-gray-700/50 rounded-xl p-6 hover:bg-white/90 dark:hover:bg-gray-900/90 transition-all duration-200 group"
            >
              <div className="flex flex-col items-center space-y-4">
                <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center group-hover:bg-purple-200 dark:group-hover:bg-purple-900/50 transition-colors">
                  <Lock className="w-8 h-8 text-purple-600 dark:text-purple-400" />
                </div>
                <div className="text-center">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    登录页面
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    现代化登录界面
                  </p>
                </div>
              </div>
            </Link>

            {/* 注册页面 */}
            <Link
              to="/register"
              className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-white/30 dark:border-gray-700/50 rounded-xl p-6 hover:bg-white/90 dark:hover:bg-gray-900/90 transition-all duration-200 group"
            >
              <div className="flex flex-col items-center space-y-4">
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors">
                  <User className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-center">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    注册页面
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    分步骤注册流程
                  </p>
                </div>
              </div>
            </Link>

            {/* 忘记密码页面 */}
            <Link
              to="/forgot-password"
              className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-white/30 dark:border-gray-700/50 rounded-xl p-6 hover:bg-white/90 dark:hover:bg-gray-900/90 transition-all duration-200 group"
            >
              <div className="flex flex-col items-center space-y-4">
                <div className="w-16 h-16 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center group-hover:bg-orange-200 dark:group-hover:bg-orange-900/50 transition-colors">
                  <Mail className="w-8 h-8 text-orange-600 dark:text-orange-400" />
                </div>
                <div className="text-center">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    忘记密码
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    密码重置流程
                  </p>
                </div>
              </div>
            </Link>

            {/* 404页面 */}
            <Link
              to="/non-existent-page"
              className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-white/30 dark:border-gray-700/50 rounded-xl p-6 hover:bg-white/90 dark:hover:bg-gray-900/90 transition-all duration-200 group"
            >
              <div className="flex flex-col items-center space-y-4">
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center group-hover:bg-red-200 dark:group-hover:bg-red-900/50 transition-colors">
                  <Eye className="w-8 h-8 text-red-600 dark:text-red-400" />
                </div>
                <div className="text-center">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    404页面
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    页面未找到
                  </p>
                </div>
              </div>
            </Link>
          </div>

          <div className="mt-12 text-center">
            <ModernButton
              onClick={() => window.history.back()}
              variant="outline"
              size="lg"
            >
              返回主页
            </ModernButton>
          </div>

          {/* 功能特色 */}
          <div className="mt-16 bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border border-white/30 dark:border-gray-700/50 rounded-xl p-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
              功能特色
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg mx-auto mb-3 flex items-center justify-center">
                  <span className="text-purple-600 dark:text-purple-400 font-bold">✨</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Aurora背景</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">动态渐变背景特效</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg mx-auto mb-3 flex items-center justify-center">
                  <span className="text-blue-600 dark:text-blue-400 font-bold">🎨</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">玻璃态设计</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">半透明模糊效果</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg mx-auto mb-3 flex items-center justify-center">
                  <span className="text-green-600 dark:text-green-400 font-bold">⚡</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">流畅动画</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Framer Motion动效</p>
              </div>
            </div>
          </div>
        </div>
      </AuroraBackground>
    </div>
  );
} 