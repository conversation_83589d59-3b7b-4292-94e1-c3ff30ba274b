import { test, expect } from '@playwright/test';

test.describe('Debug Missing Icons', () => {
  
  test('Debug why only 6 icons show instead of 9', async ({ page }) => {
    console.log('🔍 Debugging missing icons...');
    
    // 监听控制台消息
    const consoleMessages: string[] = [];
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      console.log(`Console: ${text}`);
    });

    // 监听网络请求
    const networkRequests: string[] = [];
    page.on('request', request => {
      const url = request.url();
      if (url.includes('simpleicons') || url.includes('icon')) {
        networkRequests.push(url);
        console.log(`Network: ${url}`);
      }
    });

    // 导航到测试页面
    await page.goto('/platform-integrations-test');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // 检查DOM结构
    console.log('\n=== DOM Analysis ===');
    
    // 检查所有图片
    const allImages = page.locator('img');
    const imageCount = await allImages.count();
    console.log(`Total images: ${imageCount}`);
    
    for (let i = 0; i < imageCount; i++) {
      const img = allImages.nth(i);
      const src = await img.getAttribute('src');
      const alt = await img.getAttribute('alt');
      const visible = await img.isVisible();
      console.log(`Image ${i}: ${alt} - ${visible ? 'VISIBLE' : 'HIDDEN'} - ${src?.substring(0, 50)}...`);
    }
    
    // 检查所有div（可能包含react-icons）
    const allDivs = page.locator('div[title]');
    const divCount = await allDivs.count();
    console.log(`\nTotal titled divs: ${divCount}`);
    
    for (let i = 0; i < divCount; i++) {
      const div = allDivs.nth(i);
      const title = await div.getAttribute('title');
      const visible = await div.isVisible();
      const innerHTML = await div.innerHTML();
      console.log(`Div ${i}: ${title} - ${visible ? 'VISIBLE' : 'HIDDEN'} - ${innerHTML.substring(0, 50)}...`);
    }
    
    // 检查Cloud组件
    const cloudComponent = page.locator('.platform-icon-cloud, [data-testid="platform-integrations-cloud"]');
    const cloudExists = await cloudComponent.count();
    console.log(`\nCloud component exists: ${cloudExists > 0}`);
    
    if (cloudExists > 0) {
      const cloudHTML = await cloudComponent.first().innerHTML();
      console.log(`Cloud HTML length: ${cloudHTML.length}`);
    }
    
    // 检查canvas元素（3D效果）
    const canvas = page.locator('canvas');
    const canvasCount = await canvas.count();
    console.log(`Canvas elements: ${canvasCount}`);
    
    // 分析控制台消息
    console.log('\n=== Console Messages Analysis ===');
    const iconMessages = consoleMessages.filter(msg => 
      msg.includes('icon') || 
      msg.includes('Rendering') ||
      msg.includes('lazada') ||
      msg.includes('messenger') ||
      msg.includes('webhook')
    );
    console.log(`Icon-related console messages: ${iconMessages.length}`);
    iconMessages.forEach(msg => console.log(`  - ${msg}`));
    
    // 分析网络请求
    console.log('\n=== Network Requests Analysis ===');
    console.log(`Icon-related network requests: ${networkRequests.length}`);
    networkRequests.forEach(url => console.log(`  - ${url}`));
    
    // 检查特定缺失的图标
    console.log('\n=== Missing Icons Check ===');
    const missingIcons = ['lazada', 'messenger', 'webhook'];
    
    for (const iconName of missingIcons) {
      // 检查图片
      const iconImages = page.locator(`img[alt*="${iconName}"], img[src*="${iconName}"]`);
      const imageCount = await iconImages.count();
      
      // 检查div
      const iconDivs = page.locator(`div[title*="${iconName}"], div[title*="${iconName.charAt(0).toUpperCase() + iconName.slice(1)}"]`);
      const divCount = await iconDivs.count();
      
      console.log(`${iconName}: ${imageCount} images + ${divCount} divs = ${imageCount + divCount} total`);
      
      if (imageCount + divCount === 0) {
        console.log(`  ❌ ${iconName} is completely missing from DOM`);
      }
    }
    
    // 截图保存当前状态
    await page.screenshot({ path: 'test-results/debug-missing-icons.png', fullPage: true });
    
    console.log('\n🔍 Debug analysis completed');
  });

});
