import { useState, useEffect } from 'react';
import { getCurrentLanguage, setLanguage as setStoredLanguage } from './config';
import { AuthTranslations, enTranslations, zhTranslations, msTranslations } from './translations/auth';

// 翻译映射
const translations: Record<string, AuthTranslations> = {
  en: enTranslations,
  zh: zhTranslations,
  ms: msTranslations
};

// 获取翻译文本的辅助函数
const getNestedValue = (obj: any, path: string): string => {
  return path.split('.').reduce((current, key) => current?.[key], obj) || path;
};

export function useTranslation() {
  const [currentLanguage, setCurrentLanguage] = useState(getCurrentLanguage());

  useEffect(() => {
    const handleLanguageChange = (event: CustomEvent) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange as EventListener);
    
    return () => {
      window.removeEventListener('languageChange', handleLanguageChange as EventListener);
    };
  }, []);

  const t = (key: string, fallback?: string): string => {
    const translation = translations[currentLanguage] || translations.en;
    const value = getNestedValue(translation, key);
    return value || fallback || key;
  };

  const setLanguage = (code: string) => {
    setStoredLanguage(code);
    setCurrentLanguage(code);
  };

  return {
    t,
    currentLanguage,
    setLanguage,
    translations: translations[currentLanguage] || translations.en
  };
}

// 简化的翻译函数，用于组件外部
export const translate = (key: string, language?: string): string => {
  const lang = language || getCurrentLanguage();
  const translation = translations[lang] || translations.en;
  return getNestedValue(translation, key) || key;
};
