import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import { ModernInput } from './ModernInput';
import { ModernButton } from './ModernButton';
import { Mail, ArrowLeft, CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';

export function ModernForgotPasswordForm() {
  const { resetPassword } = useAuth();
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [sent, setSent] = useState(false);

  const validateEmail = () => {
    if (!email) {
      setError('请输入邮箱地址');
      return false;
    }
    
    if (!/\S+@\S+\.\S+/.test(email)) {
      setError('请输入有效的邮箱地址');
      return false;
    }
    
    setError('');
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateEmail()) return;
    
    setLoading(true);
    setError('');
    
    try {
      const { success, error: resetError } = await resetPassword(email);
      
      if (!success) {
        toast.error(resetError || '重置密码失败');
        setError(resetError || '重置密码失败');
      } else {
        setSent(true);
        toast.success('重置邮件已发送！');
      }
    } catch (error) {
      console.error('Reset password error:', error);
      toast.error('重置密码过程中发生错误');
      setError('重置密码过程中发生错误');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (error) {
      setError('');
    }
  };

  const handleResend = () => {
    setSent(false);
    setEmail('');
  };

  if (sent) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="text-center space-y-6"
      >
        {/* 成功图标 */}
        <div className="flex justify-center">
          <div className="w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
            <CheckCircle className="w-10 h-10 text-green-600 dark:text-green-400" />
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            邮件已发送
          </h3>
          <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
            我们已向 <span className="font-medium text-purple-600 dark:text-purple-400">{email}</span> 发送了重置密码的链接。
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            请检查您的邮箱（包括垃圾邮件文件夹），然后点击链接重置密码。
          </p>
        </div>

        <div className="space-y-4">
          <ModernButton
            variant="outline"
            size="lg"
            className="w-full"
            onClick={handleResend}
          >
            重新发送邮件
          </ModernButton>
          
          <Link
            to="/login"
            className="inline-flex items-center justify-center w-full px-6 py-3 text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-500 dark:hover:text-purple-300 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回登录
          </Link>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* 说明文字 */}
      <div className="text-center space-y-2">
        <p className="text-gray-600 dark:text-gray-300">
          输入您的邮箱地址，我们将向您发送重置密码的链接。
        </p>
      </div>

      {/* 错误信息 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </motion.div>
      )}

      {/* 重置表单 */}
      <form onSubmit={handleSubmit} className="space-y-5">
        <ModernInput
          label="邮箱地址"
          type="email"
          value={email}
          onChange={handleInputChange}
          error={error}
          leftIcon={<Mail className="w-5 h-5" />}
          autoComplete="email"
          placeholder="输入您注册时使用的邮箱"
        />

        <ModernButton
          type="submit"
          loading={loading}
          size="lg"
          className="w-full"
        >
          {loading ? '发送中...' : '发送重置链接'}
        </ModernButton>
      </form>

      {/* 返回登录 */}
      <div className="text-center">
        <Link
          to="/login"
          className="inline-flex items-center text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-500 dark:hover:text-purple-300 transition-colors"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          返回登录页面
        </Link>
      </div>

      {/* 需要帮助 */}
      <div className="text-center pt-4 border-t border-gray-200 dark:border-gray-700">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          遇到问题？{' '}
          <Link
            to="/support"
            className="text-purple-600 dark:text-purple-400 hover:underline"
          >
            联系客服
          </Link>
        </p>
      </div>
    </motion.div>
  );
} 