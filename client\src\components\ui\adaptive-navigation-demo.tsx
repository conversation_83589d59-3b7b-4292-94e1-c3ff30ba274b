"use client"

import React, { useState } from "react"
import { useNavigate } from "react-router-dom"
import { AdaptiveNavigation } from "./adaptive-navigation"
import { Home, User, Briefcase, FileText, Phone } from "lucide-react"

// 演示导航项目
const navItems = [
  { name: 'Home', url: '#hero', icon: Home },
  { name: 'Features', url: '#features', icon: Briefcase },
  { name: 'Pricing', url: '#pricing', icon: FileText },
  { name: 'About', url: '#testimonials', icon: User },
  { name: 'Contact', url: '#cta', icon: Phone }
]

export function AdaptiveNavigationDemo() {
  const [isDarkMode, setIsDarkMode] = useState(false)
  const navigate = useNavigate()

  const handleDarkModeToggle = (darkMode: boolean) => {
    setIsDarkMode(darkMode)
    // 实际应用中应该同步到全局状态或localStorage
    if (darkMode) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      {/* 自适应导航 */}
      <AdaptiveNavigation
        items={navItems}
        isDarkMode={isDarkMode}
        onDarkModeToggle={handleDarkModeToggle}
        logo={{
          src: "/images/iterabiz-logo.png",
          alt: "iTeraBiz Logo",
          onClick: () => navigate('/')
        }}
        ctaButton={{
          text: "Get Started",
          onClick: () => navigate('/register')
        }}
      />

      {/* 演示内容区域 */}
      <div className="pt-20 md:pt-16">
        {/* Hero Section */}
        <section id="hero" className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
          <div className="text-center space-y-6 px-4">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white">
              自适应导航演示
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              在桌面端保持原有的tubelight导航体验，在移动端使用流体菜单解决空间竞争问题
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-8">
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">桌面端特性</h3>
                <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                  <li>• 保持原有tubelight导航</li>
                  <li>• Logo和CTA按钮位置不变</li>
                  <li>• 完整的导航体验</li>
                </ul>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">移动端特性</h3>
                <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                  <li>• 顶部简化布局</li>
                  <li>• 右下角流体菜单</li>
                  <li>• 左下角暗色模式切换</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="min-h-screen flex items-center justify-center bg-white dark:bg-gray-900">
          <div className="text-center space-y-6 px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
              功能特性
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                <Briefcase className="w-8 h-8 text-purple-600 mb-4 mx-auto" />
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">响应式设计</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  自动适应不同屏幕尺寸，提供最佳用户体验
                </p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                <FileText className="w-8 h-8 text-blue-600 mb-4 mx-auto" />
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">流体动画</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  使用Framer Motion实现平滑的动画效果
                </p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                <User className="w-8 h-8 text-green-600 mb-4 mx-auto" />
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">用户友好</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  移动端单手操作友好，拇指易达区域
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-800">
          <div className="text-center space-y-6 px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
              实施优势
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <div className="bg-white dark:bg-gray-900 p-8 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  解决的问题
                </h3>
                <ul className="text-left space-y-3 text-gray-600 dark:text-gray-300">
                  <li>• 移动端logo、导航、CTA按钮重叠</li>
                  <li>• 小屏幕上导航选项过于拥挤</li>
                  <li>• 用户体验不一致</li>
                  <li>• 单手操作困难</li>
                </ul>
              </div>
              <div className="bg-white dark:bg-gray-900 p-8 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  带来的改善
                </h3>
                <ul className="text-left space-y-3 text-gray-600 dark:text-gray-300">
                  <li>• 清晰的视觉层次</li>
                  <li>• 空间利用最大化</li>
                  <li>• 渐进式信息展示</li>
                  <li>• 更好的可访问性</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section id="testimonials" className="min-h-screen flex items-center justify-center bg-white dark:bg-gray-900">
          <div className="text-center space-y-6 px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
              使用指南
            </h2>
            <div className="max-w-2xl mx-auto">
              <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg text-left">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
                  快速集成步骤：
                </h3>
                <ol className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li>1. 导入 AdaptiveNavigation 组件</li>
                  <li>2. 传入导航项目数组</li>
                  <li>3. 配置logo和CTA按钮</li>
                  <li>4. 替换现有导航实现</li>
                </ol>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section id="cta" className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-600 to-blue-600">
          <div className="text-center space-y-6 px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-white">
              准备开始使用？
            </h2>
            <p className="text-xl text-purple-100 max-w-2xl mx-auto">
              立即集成自适应导航，为您的用户提供更好的移动端体验
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-8">
              <button
                onClick={() => navigate('/register')}
                className="bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                开始使用
              </button>
              <button
                onClick={() => navigate('/contact')}
                className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors"
              >
                联系我们
              </button>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
} 