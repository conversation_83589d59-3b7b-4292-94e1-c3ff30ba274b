// 🌐 认证页面翻译文件

export interface AuthTranslations {
  // 通用
  common: {
    email: string;
    password: string;
    confirmPassword: string;
    firstName: string;
    lastName: string;
    company: string;
    rememberMe: string;
    loading: string;
    cancel: string;
    continue: string;
    back: string;
    next: string;
    submit: string;
    resend: string;
    refresh: string;
    help: string;
    or: string;
    and: string;
    step: string;
    seconds: string;
  };

  // 登录页面
  login: {
    title: string;
    subtitle: string;
    emailPlaceholder: string;
    passwordPlaceholder: string;
    signInButton: string;
    signingIn: string;
    forgotPassword: string;
    noAccount: string;
    signUpFree: string;
    googleSignIn: string;
    backToHome: string;
    needHelp: string;
    contactSupport: string;
    welcomeBack: string;
    loginSuccessful: string;
    loginFailed: string;
    invalidCredentials: string;
    continueWith: string;
  };

  // 注册页面
  register: {
    title: string;
    subtitle: string;
    step1Title: string;
    step2Title: string;
    firstNamePlaceholder: string;
    lastNamePlaceholder: string;
    emailPlaceholder: string;
    companyPlaceholder: string;
    passwordPlaceholder: string;
    confirmPasswordPlaceholder: string;
    createAccountButton: string;
    creatingAccount: string;
    acceptTerms: string;
    acceptMarketing: string;
    termsOfService: string;
    privacyPolicy: string;
    haveAccount: string;
    signIn: string;
    googleSignUp: string;
    passwordsMatch: string;
    registrationSuccessful: string;
    registrationFailed: string;
    passwordStrength: string;
    requirements: string;
    atLeast8Characters: string;
    containsUppercase: string;
    containsLowercase: string;
    containsNumber: string;
    containsSpecialChar: string;
  };

  // 忘记密码页面
  forgotPassword: {
    title: string;
    subtitle: string;
    description: string;
    emailPlaceholder: string;
    sendResetLink: string;
    sendingResetLink: string;
    checkEmail: string;
    emailSentTo: string;
    followInstructions: string;
    resendEmail: string;
    resending: string;
    useDifferentEmail: string;
    backToLogin: string;
    didntReceiveEmail: string;
    checkSpamFolder: string;
    correctEmail: string;
    waitFewMinutes: string;
    tryResending: string;
    emailSentTimes: string;
    resetSuccessful: string;
    resetFailed: string;
  };

  // 404页面
  notFound: {
    title: string;
    subtitle: string;
    description: string;
    goHome: string;
    goBack: string;
    refreshPage: string;
    getHelp: string;
    redirectingIn: string;
    whatCanYouDo: string;
    checkUrl: string;
    checkUrlDesc: string;
    visitHomepage: string;
    visitHomepageDesc: string;
    contactSupport: string;
    contactSupportDesc: string;
    popularPages: string;
    home: string;
    login: string;
    signUp: string;
    contact: string;
    lostInSpace: string;
    closeSuggestions: string;
    popularPagesTitle: string;
    homeLink: string;
    loginLink: string;
    signUpLink: string;
    contactLink: string;
    checkURLSuggestion: string;
    returnToHomeSuggestion: string;
    contactSupportSuggestion: string;
  };

  // 错误信息
  errors: {
    required: string;
    invalidEmail: string;
    passwordTooShort: string;
    passwordsNotMatch: string;
    termsRequired: string;
    generalError: string;
    networkError: string;
    serverError: string;
  };

  // 成功信息
  success: {
    loginSuccess: string;
    registerSuccess: string;
    resetEmailSent: string;
    passwordReset: string;
  };

  // 价格信息
  pricing: {
    monthly: string;
    annualBilling: string;
    save20: string;
  };
}

// 英文翻译（默认）
export const enTranslations: AuthTranslations = {
  common: {
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    firstName: 'First Name',
    lastName: 'Last Name',
    company: 'Company',
    rememberMe: 'Remember me',
    loading: 'Loading...',
    cancel: 'Cancel',
    continue: 'Continue',
    back: 'Back',
    next: 'Next',
    submit: 'Submit',
    resend: 'Resend',
    refresh: 'Refresh',
    help: 'Help',
    or: 'or',
    and: 'and',
    step: 'Step',
    seconds: 'seconds'
  },

  login: {
    title: 'Welcome Back',
    subtitle: 'Sign in to your IteraBiz account to continue your AI-powered journey',
    emailPlaceholder: 'Enter your email address',
    passwordPlaceholder: 'Enter your password',
    signInButton: 'Sign In',
    signingIn: 'Signing in...',
    forgotPassword: 'Forgot password?',
    noAccount: "Don't have an account?",
    signUpFree: 'Sign up for free',
    googleSignIn: 'Continue with Google',
    backToHome: '← Back to Home',
    needHelp: 'Need help?',
    contactSupport: 'Contact support',
    welcomeBack: 'Welcome back! Login successful.',
    loginSuccessful: 'Login successful',
    loginFailed: 'Login failed. Please check your credentials.',
    invalidCredentials: 'Invalid email or password',
    continueWith: 'or continue with'
  },

  register: {
    title: 'Create Your Account',
    subtitle: 'Join IteraBiz and start your AI-powered content creation journey',
    step1Title: 'Personal Information',
    step2Title: 'Security & Terms',
    firstNamePlaceholder: 'John',
    lastNamePlaceholder: 'Doe',
    emailPlaceholder: '<EMAIL>',
    companyPlaceholder: 'Your company name',
    passwordPlaceholder: 'Create a strong password',
    confirmPasswordPlaceholder: 'Confirm your password',
    createAccountButton: 'Create Account',
    creatingAccount: 'Creating Account...',
    acceptTerms: 'I agree to the Terms of Service and Privacy Policy',
    acceptMarketing: "I'd like to receive product updates and marketing communications",
    termsOfService: 'Terms of Service',
    privacyPolicy: 'Privacy Policy',
    haveAccount: 'Already have an account?',
    signIn: 'Sign in',
    googleSignUp: 'Sign up with Google',
    passwordsMatch: 'Passwords match',
    registrationSuccessful: 'Welcome to IteraBiz! Registration successful.',
    registrationFailed: 'Registration failed. Please try again.',
    passwordStrength: 'Password strength',
    requirements: 'Requirements:',
    atLeast8Characters: 'At least 8 characters',
    containsUppercase: 'Contains uppercase letter',
    containsLowercase: 'Contains lowercase letter',
    containsNumber: 'Contains number',
    containsSpecialChar: 'Contains special character'
  },

  forgotPassword: {
    title: 'Reset Your Password',
    subtitle: "We'll help you get back into your account",
    description: 'Enter your email address and we\'ll send you a link to reset your password.',
    emailPlaceholder: 'Enter your email address',
    sendResetLink: 'Send Reset Link',
    sendingResetLink: 'Sending Reset Link...',
    checkEmail: 'Check Your Email',
    emailSentTo: 'We\'ve sent a password reset link to',
    followInstructions: 'Please follow the instructions in the email to complete the process.',
    resendEmail: 'Resend Email',
    resending: 'Resending...',
    useDifferentEmail: 'Use Different Email',
    backToLogin: 'Back to Login',
    didntReceiveEmail: "Didn't receive the email?",
    checkSpamFolder: 'Check your spam or junk folder',
    correctEmail: 'Make sure the email address is correct',
    waitFewMinutes: 'Wait a few minutes for the email to arrive',
    tryResending: 'Try resending the email',
    emailSentTimes: 'Email sent',
    resetSuccessful: 'Password reset email sent successfully!',
    resetFailed: 'Failed to send reset email. Please try again.'
  },

  notFound: {
    title: 'Page Not Found',
    subtitle: "The page you're looking for doesn't exist or has been moved",
    description: "The page you're looking for might have been removed, renamed, or is temporarily unavailable.",
    goHome: 'Go Home',
    goBack: 'Go Back',
    refreshPage: 'Refresh Page',
    getHelp: 'Get Help',
    redirectingIn: 'Redirecting to home page in',
    whatCanYouDo: 'What can you do?',
    checkUrl: 'Check the URL',
    checkUrlDesc: 'Make sure the web address is spelled correctly',
    visitHomepage: 'Visit our homepage',
    visitHomepageDesc: 'Start fresh from our main page and navigate from there',
    contactSupport: 'Contact support',
    contactSupportDesc: 'If you believe this is an error, contact our support team',
    popularPages: 'Popular pages:',
    home: 'Home',
    login: 'Login',
    signUp: 'Sign Up',
    contact: 'Contact',
    lostInSpace: 'Oops! This page seems to be lost in space',
    closeSuggestions: 'Close suggestions',
    popularPagesTitle: 'Popular pages:',
    homeLink: 'Home',
    loginLink: 'Login',
    signUpLink: 'Sign Up',
    contactLink: 'Contact',
    checkURLSuggestion: 'Check the URL for typos or errors, or try to {refreshLink}.',
    returnToHomeSuggestion: 'Return to our {homeLink}.',
    contactSupportSuggestion: 'If you believe this is an error, {contactLink}.'
  },

  errors: {
    required: 'This field is required',
    invalidEmail: 'Please enter a valid email address',
    passwordTooShort: 'Password must be at least 8 characters long',
    passwordsNotMatch: 'Passwords do not match',
    termsRequired: 'You must accept the terms and conditions',
    generalError: 'An unexpected error occurred. Please try again later.',
    networkError: 'Network error. Please check your connection.',
    serverError: 'Server error. Please try again later.'
  },

  success: {
    loginSuccess: 'Login successful!',
    registerSuccess: 'Registration successful!',
    resetEmailSent: 'Password reset email sent!',
    passwordReset: 'Password has been reset successfully!'
  },

  pricing: {
    monthly: 'Monthly',
    annualBilling: 'Annual billing',
    save20: 'Save 20%'
  }
};

// 中文翻译
export const zhTranslations: AuthTranslations = {
  common: {
    email: '邮箱',
    password: '密码',
    confirmPassword: '确认密码',
    firstName: '名',
    lastName: '姓',
    company: '公司',
    rememberMe: '记住我',
    loading: '加载中...',
    cancel: '取消',
    continue: '继续',
    back: '返回',
    next: '下一步',
    submit: '提交',
    resend: '重新发送',
    refresh: '刷新',
    help: '帮助',
    or: '或',
    and: '和',
    step: '步骤',
    seconds: '秒'
  },

  login: {
    title: '欢迎回来',
    subtitle: '登录您的 IteraBiz 帐户，继续您的 AI 创作之旅',
    emailPlaceholder: '请输入您的邮箱地址',
    passwordPlaceholder: '请输入您的密码',
    signInButton: '登录',
    signingIn: '登录中...',
    forgotPassword: '忘记密码？',
    noAccount: '还没有帐户？',
    signUpFree: '免费注册',
    googleSignIn: '使用Google登录',
    backToHome: '← 返回首页',
    needHelp: '需要帮助？',
    contactSupport: '联系客服',
    welcomeBack: '欢迎回来！登录成功。',
    loginSuccessful: '登录成功',
    loginFailed: '登录失败，请检查您的凭据。',
    invalidCredentials: '邮箱或密码无效',
    continueWith: '或继续使用'
  },

  register: {
    title: '创建您的帐户',
    subtitle: '加入 IteraBiz，开始您的 AI 驱动的内容创作之旅',
    step1Title: '个人信息',
    step2Title: '安全与条款',
    firstNamePlaceholder: '张',
    lastNamePlaceholder: '三',
    emailPlaceholder: '<EMAIL>',
    companyPlaceholder: '您的公司名称',
    passwordPlaceholder: '创建一个强密码',
    confirmPasswordPlaceholder: '确认您的密码',
    createAccountButton: '创建账户',
    creatingAccount: '正在创建账户...',
    acceptTerms: '我同意服务条款和隐私政策',
    acceptMarketing: '我希望接收产品更新和营销通讯',
    termsOfService: '服务条款',
    privacyPolicy: '隐私政策',
    haveAccount: '已经有账户了？',
    signIn: '登录',
    googleSignUp: '使用 Google 注册',
    passwordsMatch: '密码匹配',
    registrationSuccessful: '欢迎来到 IteraBiz！注册成功。',
    registrationFailed: '注册失败，请重试。',
    passwordStrength: '密码强度',
    requirements: '要求:',
    atLeast8Characters: '至少8个字符',
    containsUppercase: '包含大写字母',
    containsLowercase: '包含小写字母',
    containsNumber: '包含数字',
    containsSpecialChar: '包含特殊字符'
  },

  forgotPassword: {
    title: '重置您的密码',
    subtitle: '我们将帮助您重新进入您的帐户',
    description: '请输入您的电子邮件地址，我们将向您发送重置密码的链接。',
    emailPlaceholder: '请输入您的邮箱地址',
    sendResetLink: '发送重置链接',
    sendingResetLink: '正在发送重置链接...',
    checkEmail: '请检查您的邮箱',
    emailSentTo: '我们已将密码重置链接发送至',
    followInstructions: '请按照邮件中的说明完成操作。',
    resendEmail: '重新发送邮件',
    resending: '重新发送中...',
    useDifferentEmail: '使用其他邮箱',
    backToLogin: '返回登录',
    didntReceiveEmail: '没有收到邮件？',
    checkSpamFolder: '检查您的垃圾邮件或垃圾文件夹',
    correctEmail: '确保电子邮件地址正确无误',
    waitFewMinutes: '请稍候几分钟再查收邮件',
    tryResending: '尝试重新发送邮件',
    emailSentTimes: '邮件已发送',
    resetSuccessful: '密码重置邮件已成功发送！',
    resetFailed: '发送重置邮件失败，请重试。'
  },

  notFound: {
    title: '页面未找到',
    subtitle: '您要查找的页面不存在或已被移动',
    description: '您要查找的页面可能已被删除、重命名或暂时不可用。',
    goHome: '返回首页',
    goBack: '返回上页',
    refreshPage: '刷新页面',
    getHelp: '获取帮助',
    redirectingIn: '将在',
    whatCanYouDo: '您可以做什么？',
    checkUrl: '检查网址',
    checkUrlDesc: '确保网址拼写正确',
    visitHomepage: '访问我们的首页',
    visitHomepageDesc: '从我们的主页重新开始导航',
    contactSupport: '联系客服',
    contactSupportDesc: '如果您认为这是一个错误，请联系我们的客服团队',
    popularPages: '热门页面：',
    home: '首页',
    login: '登录',
    signUp: '注册',
    contact: '联系我们',
    lostInSpace: '哎呀！这个页面似乎迷失在太空中了',
    closeSuggestions: '关闭建议',
    popularPagesTitle: '热门页面：',
    homeLink: '首页',
    loginLink: '登录',
    signUpLink: '注册',
    contactLink: '联系我们',
    checkURLSuggestion: '检查网址是否有拼写错误，或尝试{refreshLink}。',
    returnToHomeSuggestion: '返回我们的{homeLink}。',
    contactSupportSuggestion: '如果您认为这是一个错误，请{contactLink}。'
  },

  errors: {
    required: '此字段为必填项',
    invalidEmail: '请输入有效的邮箱地址',
    passwordTooShort: '密码长度必须至少为8个字符',
    passwordsNotMatch: '两次输入的密码不匹配',
    termsRequired: '您必须接受条款和条件',
    generalError: '发生意外错误，请稍后重试。',
    networkError: '网络错误，请检查您的连接。',
    serverError: '服务器错误，请稍后重试。'
  },

  success: {
    loginSuccess: '登录成功！',
    registerSuccess: '注册成功！',
    resetEmailSent: '密码重置邮件已发送！',
    passwordReset: '密码已成功重置！'
  },

  pricing: {
    monthly: '月度',
    annualBilling: '年度订阅',
    save20: '节省20%'
  }
};

// 马来文翻译
export const msTranslations: AuthTranslations = {
  common: {
    email: 'E-mel',
    password: 'Kata Laluan',
    confirmPassword: 'Sahkan Kata Laluan',
    firstName: 'Nama Pertama',
    lastName: 'Nama Akhir',
    company: 'Syarikat',
    rememberMe: 'Ingat saya',
    loading: 'Memuatkan...',
    cancel: 'Batal',
    continue: 'Teruskan',
    back: 'Kembali',
    next: 'Seterusnya',
    submit: 'Hantar',
    resend: 'Hantar Semula',
    refresh: 'Muat Semula',
    help: 'Bantuan',
    or: 'atau',
    and: 'dan',
    step: 'Langkah',
    seconds: 'saat'
  },
  
  login: {
    title: 'Selamat Kembali',
    subtitle: 'Log masuk ke akaun IteraBiz anda untuk meneruskan perjalanan kandungan berkuasa AI anda',
    emailPlaceholder: 'Masukkan alamat e-mel anda',
    passwordPlaceholder: 'Masukkan kata laluan anda',
    signInButton: 'Log Masuk',
    signingIn: 'Mendaftar masuk...',
    forgotPassword: 'Lupa kata laluan?',
    noAccount: 'Tiada akaun?',
    signUpFree: 'Daftar secara percuma',
    googleSignIn: 'Teruskan dengan Google',
    backToHome: '← Kembali ke Laman Utama',
    needHelp: 'Perlukan bantuan?',
    contactSupport: 'Hubungi sokongan',
    welcomeBack: 'Selamat kembali! Log masuk berjaya.',
    loginSuccessful: 'Log masuk berjaya',
    loginFailed: 'Log masuk gagal. Sila semak kelayakan anda.',
    invalidCredentials: 'Emel atau kata laluan tidak sah',
    continueWith: 'atau teruskan dengan'
  },

  register: {
    title: 'Cipta Akaun Anda',
    subtitle: 'Sertai IteraBiz dan mulakan perjalanan penciptaan kandungan berkuasa AI anda',
    step1Title: 'Maklumat Peribadi',
    step2Title: 'Keselamatan & Syarat',
    firstNamePlaceholder: 'Ahmad',
    lastNamePlaceholder: 'bin Abdullah',
    emailPlaceholder: '<EMAIL>',
    companyPlaceholder: 'Nama syarikat anda',
    passwordPlaceholder: 'Cipta kata laluan yang kukuh',
    confirmPasswordPlaceholder: 'Sahkan kata laluan anda',
    createAccountButton: 'Cipta Akaun',
    creatingAccount: 'Mencipta Akaun...',
    acceptTerms: 'Saya bersetuju dengan Syarat Perkhidmatan dan Dasar Privasi',
    acceptMarketing: 'Saya ingin menerima kemas kini produk dan komunikasi pemasaran',
    termsOfService: 'Syarat Perkhidmatan',
    privacyPolicy: 'Dasar Privasi',
    haveAccount: 'Sudah mempunyai akaun?',
    signIn: 'Log Masuk',
    googleSignUp: 'Daftar dengan Google',
    passwordsMatch: 'Kata laluan sepadan',
    registrationSuccessful: 'Selamat datang ke IteraBiz! Pendaftaran berjaya.',
    registrationFailed: 'Pendaftaran gagal. Sila cuba lagi.',
    passwordStrength: 'Kekuatan kata laluan',
    requirements: 'Keperluan:',
    atLeast8Characters: 'Sekurang-kurangnya 8 aksara',
    containsUppercase: 'Mengandungi huruf besar',
    containsLowercase: 'Mengandungi huruf kecil',
    containsNumber: 'Mengandungi nombor',
    containsSpecialChar: 'Mengandungi aksara khas'
  },

  forgotPassword: {
    title: 'Menetapkan Semula Kata Laluan Anda',
    subtitle: 'Kami akan membantu anda masuk semula ke akaun anda',
    description: 'Masukkan alamat e-mel anda dan kami akan menghantar pautan untuk menetapkan semula kata laluan anda.',
    emailPlaceholder: 'Masukkan alamat e-mel anda',
    sendResetLink: 'Hantar Pautan Tetapan Semula',
    sendingResetLink: 'Menghantar Pautan Tetapan Semula...',
    checkEmail: 'Semak E-mel Anda',
    emailSentTo: 'Kami telah menghantar pautan tetapan semula kata laluan ke',
    followInstructions: 'Sila ikut arahan dalam e-mel untuk melengkapkan proses tersebut.',
    resendEmail: 'Hantar Semula E-mel',
    resending: 'Menghantar semula...',
    useDifferentEmail: 'Gunakan E-mel Lain',
    backToLogin: 'Kembali ke Log Masuk',
    didntReceiveEmail: 'Tidak menerima e-mel?',
    checkSpamFolder: 'Semak folder spam atau sampah anda',
    correctEmail: 'Pastikan alamat e-mel adalah betul',
    waitFewMinutes: 'Tunggu beberapa minit untuk e-mel tiba',
    tryResending: 'Cuba hantar semula e-mel',
    emailSentTimes: 'E-mel telah dihantar',
    resetSuccessful: 'E-mel tetapan semula kata laluan telah berjaya dihantar!',
    resetFailed: 'Gagal menghantar e-mel tetapan semula. Sila cuba lagi.'
  },
  
  notFound: {
    title: 'Halaman Tidak Dijumpai',
    subtitle: 'Halaman yang anda cari tidak wujud atau telah dipindahkan',
    description: 'Halaman yang anda cari mungkin telah dipadamkan, dinamakan semula, atau tidak tersedia buat sementara waktu.',
    goHome: 'Pergi ke Laman Utama',
    goBack: 'Kembali',
    refreshPage: 'Muat Semula Halaman',
    getHelp: 'Dapatkan Bantuan',
    redirectingIn: 'Mengalihkan ke laman utama dalam',
    whatCanYouDo: 'Apa yang boleh anda lakukan?',
    checkUrl: 'Semak URL',
    checkUrlDesc: 'Pastikan alamat web dieja dengan betul',
    visitHomepage: 'Lawati laman utama kami',
    visitHomepageDesc: 'Mulakan semula dari halaman utama kami dan navigasi dari sana',
    contactSupport: 'Hubungi sokongan',
    contactSupportDesc: 'Jika anda percaya ini adalah ralat, hubungi pasukan sokongan kami',
    popularPages: 'Halaman popular:',
    home: 'Laman Utama',
    login: 'Log Masuk',
    signUp: 'Daftar',
    contact: 'Hubungi',
    lostInSpace: 'Alamak! Halaman ini nampaknya hilang di angkasa',
    closeSuggestions: 'Tutup cadangan',
    popularPagesTitle: 'Halaman popular:',
    homeLink: 'Laman Utama',
    loginLink: 'Log Masuk',
    signUpLink: 'Daftar',
    contactLink: 'Hubungi',
    checkURLSuggestion: 'Semak URL untuk kesilapan menaip, atau cuba {refreshLink}.',
    returnToHomeSuggestion: 'Kembali ke {homeLink} kami.',
    contactSupportSuggestion: 'Jika anda percaya ini adalah ralat, {contactLink}.'
  },

  errors: {
    required: 'Medan ini diperlukan',
    invalidEmail: 'Sila masukkan alamat e-mel yang sah',
    passwordTooShort: 'Kata laluan mestilah sekurang-kurangnya 8 aksara',
    passwordsNotMatch: 'Kata laluan tidak sepadan',
    termsRequired: 'Anda mesti menerima terma dan syarat',
    generalError: 'Ralat tidak dijangka berlaku. Sila cuba lagi nanti.',
    networkError: 'Ralat rangkaian. Sila semak sambungan anda.',
    serverError: 'Ralat pelayan. Sila cuba lagi nanti.'
  },

  success: {
    loginSuccess: 'Log masuk berjaya!',
    registerSuccess: 'Pendaftaran berjaya!',
    resetEmailSent: 'E-mel tetapan semula kata laluan telah dihantar!',
    passwordReset: 'Kata laluan telah berjaya ditetapkan semula!'
  },

  pricing: {
    monthly: 'Bulanan',
    annualBilling: 'Bil tahunan',
    save20: 'Jimat 20%'
  }
};
