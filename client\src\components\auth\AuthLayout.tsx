import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { AuroraBackground } from '../ui/aurora-background';
import { Card, CardContent, CardHeader } from '../ui/card';
import { cn } from '@/lib/utils';

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
  showLogo?: boolean;
  className?: string;
  cardClassName?: string;
}

export function AuthLayout({ 
  children, 
  title, 
  subtitle,
  showLogo = true,
  className,
  cardClassName 
}: AuthLayoutProps) {
  return (
    <>
      <Helmet>
        <title>{title} - iTeraBiz</title>
      </Helmet>
      
      <AuroraBackground 
        className={cn("min-h-screen flex items-center justify-center p-4", className)}
        showRadialGradient={true}
      >
        <div className="w-full max-w-md mx-auto">
          <Card className={cn(
            "backdrop-blur-sm bg-white/80 dark:bg-gray-900/80 border border-white/20 dark:border-gray-700/50 shadow-2xl",
            cardClassName
          )}>
            {(showLogo || title || subtitle) && (
              <CardHeader className="text-center space-y-4 pb-8">
                {showLogo && (
                  <Link to="/home" className="inline-block">
                    <div className="flex items-center justify-center">
                      <div className="w-12 h-12 bg-gradient-to-br from-purple-500 via-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                        <span className="text-white font-bold text-xl">iT</span>
                      </div>
                    </div>
                  </Link>
                )}
                
                {title && (
                  <div className="space-y-2">
                    <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white">
                      {title}
                    </h1>
                    {subtitle && (
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {subtitle}
                      </p>
                    )}
                  </div>
                )}
              </CardHeader>
            )}
            
            <CardContent className="px-8 pb-8">
              {children}
            </CardContent>
          </Card>
          
          {/* 品牌信息 */}
          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              © 2025 iTeraBiz. All rights reserved.
            </p>
          </div>
        </div>
      </AuroraBackground>
    </>
  );
} 