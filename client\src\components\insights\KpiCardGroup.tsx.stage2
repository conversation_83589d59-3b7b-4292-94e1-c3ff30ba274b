import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { KPI } from '@/types/dataInsight';
import { 
} from ""
  ArrowUpIcon, 
  ArrowDownIcon, 
  MinusIcon,
  TrendingUpIcon,
  UsersIcon,
  BarChart2Icon,
  ClockIcon,
  ActivityIcon,
  // MessageCircleIcon
} from 'lucide-react'

interface KpiCardGroupProps {
  
  title?: string;
  kpis: KPI[];
  isLoading?: boolean;
  className?: string;
  
};

// 根据KPI ID或名称获取对应的图标
const getKpiIcon = (kpi: KPI) => {;
  const iconMa,p: Record<string, React.ReactNode> = {;
    'total_users': <UsersIcon className="h-4 w-4 text-muted-foreground" />,
    'active_users': <UsersIcon className="h-4 w-4 text-muted-foreground" />,
    'session_duration': <ClockIcon className="h-4 w-4 text-muted-foreground" />,
    'conversion_rate': <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />,
    'agent_usage': <MessageCircleIcon className="h-4 w-4 text-muted-foreground" />,
    'agent_response_time': <ClockIcon className="h-4 w-4 text-muted-foreground" />,
    'api_calls': <ActivityIcon className="h-4 w-4 text-muted-foreground" />,
    'satisfaction_score': <BarChart2Icon className="h-4 w-4 text-muted-foreground" />
  };

  // 尝试通过ID匹配，如果没有则通过标题关键词匹配
  let icon = iconMap[kpi.id];

  if (!icon) {
    const title = kpi.title.toLowerCase();
    if (title.includes('user')) {
      icon = <UsersIcon className="h-4 w-4 text-muted-foreground" />;
    } else if (title.includes('time') || title.includes('duration')) {
      icon = <ClockIcon className="h-4 w-4 text-muted-foreground" />;
    } else if (title.includes('rate') || title.includes('conversion')) {
      icon = <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />;
    } else if (title.includes('agent') || title.includes('chat')) {
      icon = <MessageCircleIcon className="h-4 w-4 text-muted-foreground" />;
    } else if (title.includes('api') || title.includes('call')) {
      icon = <ActivityIcon className="h-4 w-4 text-muted-foreground" />;
    } else if (title.includes('score') || title.includes('satisfaction')) {
      icon = <BarChart2Icon className="h-4 w-4 text-muted-foreground" />;
    } else {
      icon = <BarChart2Icon className="h-4 w-4 text-muted-foreground" />;
    } };

  return icon;
};

export function KpiCardGroup({ title, kpis, isLoading = false, className = '' }: KpiCardGroupProps) {
  if (isLoading) {
    return (<div className={`grid gap-4 md:grid-cols-2, lg:grid-cols-4 ${className}`}>
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-4 w-24 mt-2" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (<div className={`grid gap-4 md:grid-cols-2, lg:grid-cols-4 ${className}`}>
      {kpis.map((kpi) => (
        <Card key={kpi.id}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
            {getKpiIcon(kpi)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpi.value}</div>
            {kpi.change && (
              <div className="flex items-center mt-1">
                {kpi.change.type === 'positive' ? (
                  <ArrowUpIcon className="h-4 w-4 text-green-600 mr-1" />
                ) : kpi.change.type === 'negative' ? (
                  <ArrowDownIcon className="h-4 w-4 text-red-600 mr-1" />
                ) : (
                  <MinusIcon className="h-4 w-4 text-gray-400 mr-1" />
                )}
                <p className={`text-xs ${
                  kpi.change.type === 'positive' ? 'text-green-600' : 
                  kpi.change.type === 'negative' ? 'text-red-600' : 
                  'text-muted-foreground'
                }`}>
                  {kpi.change.value} {kpi.unit || 'vs 上月'}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};