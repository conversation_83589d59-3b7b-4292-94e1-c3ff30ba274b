import { test, expect } from '@playwright/test';

test.describe('占位符与图标叠加问题检查', () => {
  test.beforeEach(async ({ page }) => {
    await page.waitForLoadState('networkidle');
  });

  test('登录页面 - 检查占位符与图标是否叠加', async ({ page }) => {
    await page.goto('http://localhost:3003/login');
    await page.waitForLoadState('networkidle');
    
    // 检查邮箱输入框
    const emailInput = page.locator('input[type="email"]');
    const emailIcon = page.locator('input[type="email"]').locator('..').locator('div').first();
    
    // 获取输入框和图标的位置信息
    const emailInputBox = await emailInput.boundingBox();
    const emailIconBox = await emailIcon.boundingBox();
    
    console.log('Email Input Box:', emailInputBox);
    console.log('Email Icon Box:', emailIconBox);
    
    // 检查占位符文字是否可见
    await expect(emailInput).toHaveAttribute('placeholder', '<EMAIL>');
    
    // 检查密码输入框
    const passwordInput = page.locator('input[type="password"]');
    const passwordIcon = page.locator('input[type="password"]').locator('..').locator('div').first();
    
    // 获取密码输入框和图标的位置信息
    const passwordInputBox = await passwordInput.boundingBox();
    const passwordIconBox = await passwordIcon.boundingBox();
    
    console.log('Password Input Box:', passwordInputBox);
    console.log('Password Icon Box:', passwordIconBox);
    
    // 检查占位符文字是否可见
    await expect(passwordInput).toHaveAttribute('placeholder', '••••••••');
    
    // 截图以便检查视觉效果
    await page.screenshot({ path: 'test-results/login-placeholder-check.png' });
  });

  test('注册页面第一步 - 检查占位符与图标是否叠加', async ({ page }) => {
    await page.goto('http://localhost:3003/register');
    await page.waitForLoadState('networkidle');
    
    // 检查所有输入框的占位符
    const firstNameInput = page.locator('input[placeholder="First name"]');
    const lastNameInput = page.locator('input[placeholder="Last name"]');
    const emailInput = page.locator('input[placeholder="<EMAIL>"]');
    const companyInput = page.locator('input[placeholder="Company name (optional)"]');
    
    // 验证占位符存在
    await expect(firstNameInput).toBeVisible();
    await expect(lastNameInput).toBeVisible();
    await expect(emailInput).toBeVisible();
    await expect(companyInput).toBeVisible();
    
    // 检查输入框的样式
    const firstNameStyles = await firstNameInput.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        paddingLeft: styles.paddingLeft,
        textIndent: styles.textIndent
      };
    });
    
    console.log('First Name Input Styles:', firstNameStyles);
    
    // 截图以便检查视觉效果
    await page.screenshot({ path: 'test-results/register-step1-placeholder-check.png' });
  });

  test('注册页面第二步 - 检查密码占位符与图标是否叠加', async ({ page }) => {
    await page.goto('http://localhost:3003/register');
    await page.waitForLoadState('networkidle');
    
    // 填写第一步表单
    await page.fill('input[placeholder="First name"]', 'Test');
    await page.fill('input[placeholder="Last name"]', 'User');
    await page.fill('input[placeholder="<EMAIL>"]', '<EMAIL>');
    
    // 点击Continue按钮进入第二步
    await page.click('button:has-text("Continue")');
    await page.waitForTimeout(1000);
    
    // 检查密码字段占位符
    const passwordInputs = page.locator('input[placeholder="••••••••"]');
    await expect(passwordInputs).toHaveCount(2);
    
    // 检查第一个密码输入框的样式
    const passwordInput = passwordInputs.first();
    const passwordStyles = await passwordInput.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        paddingLeft: styles.paddingLeft,
        textIndent: styles.textIndent
      };
    });
    
    console.log('Password Input Styles:', passwordStyles);
    
    // 截图以便检查视觉效果
    await page.screenshot({ path: 'test-results/register-step2-placeholder-check.png' });
  });

  test('忘记密码页面 - 检查占位符与图标是否叠加', async ({ page }) => {
    await page.goto('http://localhost:3003/forgot-password');
    await page.waitForLoadState('networkidle');
    
    // 检查邮箱输入框
    const emailInput = page.locator('input[placeholder="<EMAIL>"]');
    await expect(emailInput).toBeVisible();
    
    // 检查输入框的样式
    const emailStyles = await emailInput.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        paddingLeft: styles.paddingLeft,
        textIndent: styles.textIndent
      };
    });
    
    console.log('Forgot Password Email Input Styles:', emailStyles);
    
    // 截图以便检查视觉效果
    await page.screenshot({ path: 'test-results/forgot-password-placeholder-check.png' });
  });

  test('检查输入框内部元素的层级和位置', async ({ page }) => {
    await page.goto('http://localhost:3003/login');
    await page.waitForLoadState('networkidle');
    
    // 检查邮箱输入框容器的结构
    const emailContainer = page.locator('input[type="email"]').locator('..');
    
    // 获取容器内所有子元素
    const childElements = await emailContainer.locator('*').all();
    
    for (let i = 0; i < childElements.length; i++) {
      const element = childElements[i];
      const tagName = await element.evaluate(el => el.tagName);
      const className = await element.evaluate(el => el.className);
      const styles = await element.evaluate(el => {
        const computedStyles = window.getComputedStyle(el);
        return {
          position: computedStyles.position,
          left: computedStyles.left,
          top: computedStyles.top,
          zIndex: computedStyles.zIndex,
          paddingLeft: computedStyles.paddingLeft
        };
      });
      
      console.log(`Element ${i}: ${tagName}, Class: ${className}, Styles:`, styles);
    }
  });

  test('测试输入框焦点状态下的占位符行为', async ({ page }) => {
    await page.goto('http://localhost:3003/login');
    await page.waitForLoadState('networkidle');
    
    const emailInput = page.locator('input[type="email"]');
    
    // 点击输入框获得焦点
    await emailInput.click();
    
    // 检查焦点状态下的样式
    const focusedStyles = await emailInput.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        paddingLeft: styles.paddingLeft,
        textIndent: styles.textIndent,
        color: styles.color
      };
    });
    
    console.log('Focused Email Input Styles:', focusedStyles);
    
    // 截图焦点状态
    await page.screenshot({ path: 'test-results/login-focused-placeholder-check.png' });
  });
});
