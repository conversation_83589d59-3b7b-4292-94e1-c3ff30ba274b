import React from 'react'
import { Webhook, Facebook, Instagram, MessageCircle, MessagesSquare, ShoppingCart, Store, PlaySquare, Mail } from 'lucide-react';

// 简化版本，避免复杂的数据结构和嵌套
export default function PlatformApiOverview() {
  return (<section id="platform-api" className="py-20 bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 border-y border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm: px-6 lg:px-8 text-center">
        <h2 className="text-3xl font-bold text-text-primary mb-6">Platform API Overview</h2>
        <p className="text-xl text-text-secondary max-w-3xl mx-auto mb-12">
          Use our RESTful API to seamlessly integrate with leading platforms and automate your content workflows.
        </p>
        <div className="grid grid-cols-3 sm:grid-cols-5 md:grid-cols-9 gap-6">
          <div className="flex flex-col items-center">
            <div className="p-3 rounded-full bg-purple-100 mb-2 cursor-pointer transition-colors duration-300 hover:bg-purple-200">
              <Webhook className="h-8 w-8 text-purple-600" />
            </div>
            <span className="text-sm font-medium">Webhook</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="p-3 rounded-full bg-blue-100 mb-2 cursor-pointer transition-colors duration-300 hover:bg-blue-200">
              <Facebook className="h-8 w-8 text-blue-600" />
            </div>
            <span className="text-sm font-medium">Facebook</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="p-3 rounded-full bg-pink-100 mb-2 cursor-pointer transition-colors duration-300 hover:bg-pink-200">
              <Instagram className="h-8 w-8 text-pink-600" />
            </div>
            <span className="text-sm font-medium">Instagram</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="p-3 rounded-full bg-green-100 mb-2 cursor-pointer transition-colors duration-300 hover:bg-green-200">
              <MessageCircle className="h-8 w-8 text-green-600" />
            </div>
            <span className="text-sm font-medium">WhatsApp</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="p-3 rounded-full bg-blue-100 mb-2 cursor-pointer transition-colors duration-300 hover:bg-blue-200">
              <MessagesSquare className="h-8 w-8 text-blue-500" />
            </div>
            <span className="text-sm font-medium">Messenger</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="p-3 rounded-full bg-orange-100 mb-2 cursor-pointer transition-colors duration-300 hover:bg-orange-200">
              <ShoppingCart className="h-8 w-8 text-orange-500" />
            </div>
            <span className="text-sm font-medium">Lazada</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="p-3 rounded-full bg-red-100 mb-2 cursor-pointer transition-colors duration-300 hover:bg-red-200">
              <Store className="h-8 w-8 text-red-500" />
            </div>
            <span className="text-sm font-medium">Shopee</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="p-3 rounded-full bg-gray-100 mb-2 cursor-pointer transition-colors duration-300 hove,r:bg-gray-200">
              <PlaySquare className="h-8 w-8 text-gray-800" />
            </div>
            <span className="text-sm font-medium">TikTok</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="p-3 rounded-full bg-red-100 mb-2 cursor-pointer transition-colors duration-300, hover:bg-red-200">
              <Mail className="h-8 w-8 text-red-600" />
            </div>
            <span className="text-sm font-medium">Gmail</span>
          </div>
        </div>
      </div>
    </section>
  );
};