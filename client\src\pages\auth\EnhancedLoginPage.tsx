import React, { useState, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { toast } from 'react-toastify';
import { 
  EnhancedAuthLayout, 
  EnhancedAuthInput, 
  EnhancedAuthButton, 
  GoogleSignInButton 
} from '../../components/ui/auth';
import { Eye, EyeOff, Mail, Lock, AlertCircle } from 'lucide-react';
import { useTranslation } from '../../i18n/useTranslation';

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

interface FormErrors {
  email?: string;
  password?: string;
  general?: string;
}

export default function EnhancedLoginPage() {
  const { login, signInWithGoogle } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();
  
  // 表单状态
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    rememberMe: false
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    // 邮箱验证
    if (!formData.email) {
      newErrors.email = t('errors.required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = t('errors.invalidEmail');
    }
    
    // 密码验证
    if (!formData.password) {
      newErrors.password = t('errors.required');
    } else if (formData.password.length < 6) {
      newErrors.password = t('errors.passwordTooShort');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交 (优化性能)
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setLoading(true);
    setErrors({});
    
    try {
      const { success, error } = await login(formData.email, formData.password);
      
      if (!success) {
        const errorMessage = error || t('login.loginFailed');
        setErrors({ general: errorMessage });
        toast.error(errorMessage);
      } else {
        toast.success(t('login.welcomeBack'));
        // 导航由AuthContext处理
      }
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = t('errors.generalError');
      setErrors({ general: errorMessage });
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [login, formData.email, formData.password, t]);

  // 处理Google登录 (优化性能)
  const handleGoogleLogin = useCallback(async () => {
    setGoogleLoading(true);
    setErrors({});
    
    try {
      // 启动Google登录流程，成功提示将由AuthContext在回调后处理
      await signInWithGoogle();
    } catch (error) {
      console.error('Google login error:', error);
      const errorMessage = t('errors.generalError');
      setErrors({ general: errorMessage });
      toast.error(errorMessage);
    } finally {
      setGoogleLoading(false);
    }
  }, [signInWithGoogle, t]);

  // 处理输入变化
  const handleInputChange = (field: keyof LoginFormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = field === 'rememberMe' ? e.target.checked : e.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除对应字段的错误
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <EnhancedAuthLayout
      title={t('login.title')}
      subtitle={t('login.subtitle')}
      description={t('login.subtitle')}
      showBackButton={true}
      backButtonText={t('login.backToHome')}
      backButtonHref="/home"
      variant="default"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.2 }}
      >
        {/* 通用错误信息 */}
        <AnimatePresence>
          {errors.general && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl flex items-center gap-3"
            >
              <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
              <p className="text-sm text-red-700">{errors.general}</p>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 登录表单 */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 邮箱输入 */}
          <EnhancedAuthInput
            type="email"
            label={t('common.email')}
            placeholder={t('login.emailPlaceholder')}
            value={formData.email}
            onChange={handleInputChange('email')}
            error={errors.email}
            leftIcon={<Mail className="w-5 h-5" />}
            variant="default"
            size="md"
            required
          />

          {/* 密码输入 */}
          <EnhancedAuthInput
            type="password"
            label={t('common.password')}
            placeholder={t('login.passwordPlaceholder')}
            value={formData.password}
            onChange={handleInputChange('password')}
            error={errors.password}
            leftIcon={<Lock className="w-5 h-5" />}
            showPasswordToggle={true}
            variant="default"
            size="md"
            required
          />

          {/* 记住我和忘记密码 */}
          <div className="flex items-center justify-between">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.rememberMe}
                onChange={handleInputChange('rememberMe')}
                className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
              />
              <span className="text-sm text-gray-600">{t('common.rememberMe')}</span>
            </label>
            
            <Link
              to="/forgot-password"
              className="text-sm text-purple-600 hover:text-purple-700 font-medium transition-colors duration-200"
            >
              {t('login.forgotPassword')}
            </Link>
          </div>

          {/* 登录按钮 */}
          <EnhancedAuthButton
            type="submit"
            variant="primary"
            size="md"
            loading={loading}
            fullWidth={true}
            gradient={true}
            disabled={loading || googleLoading}
          >
            {loading ? t('login.signingIn') : t('login.signInButton')}
          </EnhancedAuthButton>
        </form>

        {/* 分隔线 */}
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-200" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-4 bg-white text-gray-500">{t('login.continueWith')}</span>
          </div>
        </div>

        {/* Google登录按钮 */}
        <GoogleSignInButton
          loading={googleLoading}
          onGoogleSignIn={handleGoogleLogin}
          disabled={loading || googleLoading}
          variant="default"
          size="md"
          fullWidth={true}
        />

        {/* 注册链接 */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600">
            {t('login.noAccount')}{' '}
            <Link
              to="/register"
              className="text-purple-600 hover:text-purple-700 font-semibold transition-colors duration-200"
            >
              {t('login.signUpFree')}
            </Link>
          </p>
        </div>

        {/* 帮助链接 */}
        <div className="mt-6 text-center">
          <div className="flex items-center justify-center gap-4 text-xs text-gray-400">
            <Link to="/help" className="hover:text-gray-600 transition-colors">
              {t('common.needHelp')}
            </Link>
            <span>•</span>
            <Link to="/contact" className="hover:text-gray-600 transition-colors">
              {t('common.contactSupport')}
            </Link>
          </div>
        </div>
      </motion.div>
    </EnhancedAuthLayout>
  );
}
