import React from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { motion } from 'framer-motion';
import { AuroraBackground } from '../aurora-background';
import { Card, CardContent, CardHeader } from '../card';
import { cn } from '@/lib/utils';
import { authTokensV2 } from '@/design-system/tokens/auth-tokens';
import { PerformanceMonitor } from './PerformanceMonitor';
import { Sparkles, ArrowLeft } from 'lucide-react';
import { LanguageSwitcher } from '@/components/common/LanguageSwitcher';
import { useTranslation } from '@/i18n/useTranslation';

interface EnhancedAuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
  description?: string;
  showLogo?: boolean;
  showBackButton?: boolean;
  backButtonText?: string;
  backButtonHref?: string;
  className?: string;
  cardClassName?: string;
  variant?: 'default' | 'glass' | 'minimal';
}

export function EnhancedAuthLayout({ 
  children, 
  title, 
  subtitle,
  description,
  showLogo = true,
  showBackButton = false,
  backButtonText = "Back to Home",
  backButtonHref = "/home",
  className,
  cardClassName,
  variant = 'default'
}: EnhancedAuthLayoutProps) {
  
  const { t } = useTranslation();
  // 卡片变体样式 (深色主题支持)
  const cardVariants = {
    default: "backdrop-blur-md bg-white/95 dark:bg-slate-900/95 border border-purple-100/50 dark:border-purple-500/30 shadow-xl",
    glass: "backdrop-blur-xl bg-white/10 dark:bg-slate-900/80 border border-white/20 dark:border-purple-500/30 shadow-2xl",
    minimal: "bg-white dark:bg-slate-900 border border-gray-200 dark:border-gray-700 shadow-lg"
  };

  // 页面进入动画
  const pageVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
  };

  // 卡片动画
  const cardVariants_animation = {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 }
  };

  return (
    <>
      <Helmet>
        <title>{title} - IteraBiz</title>
        {description && <meta name="description" content={description} />}
      </Helmet>
      
      <div className="absolute top-4 right-4 z-20">
        <LanguageSwitcher />
      </div>

      <AuroraBackground
        className={cn("min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-gray-50 via-purple-50/30 to-purple-100/50 dark:from-gray-900 dark:via-purple-900/20 dark:to-purple-800/30", className)}
        showRadialGradient={true}
      >
        <motion.div
          className="w-full max-w-lg mx-auto px-4 sm:px-6 lg:px-8"
          variants={pageVariants}
          initial="initial"
          animate="animate"
          exit="exit"
          transition={{ duration: 0.5, ease: "easeOut" }}
        >
          {/* 返回按钮 */}
          {showBackButton && (
            <motion.div 
              className="mb-6"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              <Link 
                to={backButtonHref}
                className="inline-flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
              >
                <ArrowLeft className="w-4 h-4" />
                {backButtonText}
              </Link>
            </motion.div>
          )}

          <motion.div
            variants={cardVariants_animation}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{ duration: 0.4, delay: 0.1, ease: "easeOut" }}
          >
            <Card className={cn(
              "rounded-2xl overflow-hidden transition-all duration-300",
              "w-full max-w-lg mx-auto",
              "sm:rounded-2xl rounded-xl",
              cardVariants[variant],
              "hover:shadow-2xl hover:scale-[1.02]",
              cardClassName
            )}>
              {/* 头部 */}
              {(showLogo || title || subtitle) && (
                <CardHeader className="text-center space-y-4 pb-6 px-6 sm:px-8 pt-6">
                  {/* Logo */}
                  {showLogo && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.3, duration: 0.4, ease: "easeOut" }}
                    >
                      <Link to="/home" className="inline-block group">
                        <div className="flex items-center justify-center">
                          <div className="w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105 overflow-hidden">
                            <img
                              src="/newicon.png"
                              alt="IteraBiz Logo"
                              className="w-full h-full object-contain"
                            />
                          </div>
                        </div>
                      </Link>
                    </motion.div>
                  )}
                  
                  {/* 标题和副标题 */}
                  {title && (
                    <motion.div
                      className="space-y-2"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4, duration: 0.4 }}
                    >
                      <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white">
                        {title}
                      </h1>
                      {subtitle && (
                        <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                          {subtitle}
                        </p>
                      )}
                    </motion.div>
                  )}
                </CardHeader>
              )}
              
              {/* 内容 */}
              <CardContent className="px-6 sm:px-8 pb-6">
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.4 }}
                >
                  {children}
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>
          
          {/* 品牌信息 */}
          <motion.div 
            className="mt-8 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7, duration: 0.4 }}
          >
            <p className="text-xs text-gray-500 dark:text-gray-400">
              © 2025 IteraBiz. All rights reserved.
            </p>
            <div className="mt-2 flex items-center justify-center gap-4 text-xs text-gray-400">
              <Link to="/privacy" className="hover:text-gray-600 transition-colors">
                {t('register.privacyPolicy')}
              </Link>
              <span>•</span>
              <Link to="/terms" className="hover:text-gray-600 transition-colors">
                {t('register.termsOfService')}
              </Link>
            </div>
          </motion.div>
        </motion.div>

        {/* 性能监控 (仅开发环境) */}
        <PerformanceMonitor />
      </AuroraBackground>
    </>
  );
}
