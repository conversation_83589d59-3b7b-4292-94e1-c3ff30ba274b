import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { toast } from 'react-toastify';
import { 
  EnhancedAuthLayout, 
  EnhancedAuthInput, 
  EnhancedAuthButton, 
  GoogleSignInButton 
} from '../../components/ui/auth';
import { PasswordStrengthIndicator } from '../../components/ui/auth/PasswordStrengthIndicator';
import { User, Mail, Lock, AlertCircle, CheckCircle, Building } from 'lucide-react';
import { useTranslation } from '../../i18n/useTranslation';

interface RegisterFormData {
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
  acceptMarketing: boolean;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  company?: string;
  password?: string;
  confirmPassword?: string;
  acceptTerms?: string;
  general?: string;
}

export default function EnhancedRegisterPage() {
  const { register, signInWithGoogle } = useAuth();
  const { t } = useTranslation();
  
  // 表单状态
  const [formData, setFormData] = useState<RegisterFormData>({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    password: '',
    confirmPassword: '',
    acceptTerms: false,
    acceptMarketing: false
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [step, setStep] = useState(1); // 多步骤注册

  // 表单验证
  const validateStep1 = (): boolean => {
    const newErrors: FormErrors = {};
    
    if (!formData.firstName.trim()) {
      newErrors.firstName = t('errors.required');
    }
    
    if (!formData.lastName.trim()) {
      newErrors.lastName = t('errors.required');
    }
    
    if (!formData.email) {
      newErrors.email = t('errors.required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = t('errors.invalidEmail');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = (): boolean => {
    const newErrors: FormErrors = {};
    
    if (!formData.password) {
      newErrors.password = t('errors.required');
    } else if (formData.password.length < 8) {
      newErrors.password = t('errors.passwordTooShort');
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = t('errors.required');
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = t('errors.passwordsNotMatch');
    }
    
    if (!formData.acceptTerms) {
      newErrors.acceptTerms = t('errors.termsRequired');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理下一步
  const handleNextStep = () => {
    if (step === 1 && validateStep1()) {
      setStep(2);
    }
  };

  // 处理上一步
  const handlePrevStep = () => {
    if (step === 2) {
      setStep(1);
      setErrors({});
    }
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateStep2()) return;
    
    setLoading(true);
    setErrors({});
    
    try {
      const { success, error } = await register(
        formData.email, 
        formData.password,
        {
          firstName: formData.firstName,
          lastName: formData.lastName,
          company: formData.company,
          acceptMarketing: formData.acceptMarketing
        }
      );
      
      if (!success) {
        const errorMessage = error || t('register.registrationFailed');
        setErrors({ general: errorMessage });
        toast.error(errorMessage);
      } else {
        toast.success(t('register.registrationSuccessful'));
        // 导航由AuthContext处理
      }
    } catch (error) {
      console.error('Registration error:', error);
      const errorMessage = t('errors.generalError');
      setErrors({ general: errorMessage });
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 处理Google注册
  const handleGoogleRegister = async () => {
    setGoogleLoading(true);
    setErrors({});
    
    try {
      await signInWithGoogle();
    } catch (error) {
      console.error('Google registration error:', error);
      const errorMessage = t('errors.generalError');
      setErrors({ general: errorMessage });
      toast.error(errorMessage);
    } finally {
      setGoogleLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: keyof RegisterFormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = field === 'acceptTerms' || field === 'acceptMarketing' 
      ? e.target.checked 
      : e.target.value;
    
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除对应字段的错误
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <EnhancedAuthLayout
      title={t('register.title')}
      subtitle={t('register.subtitle')}
      description={t('register.subtitle')}
      showBackButton={true}
      backButtonText={t('login.backToHome')}
      backButtonHref="/home"
      variant="default"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.2 }}
      >
        {/* 进度指示器 */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm font-medium text-gray-600">
              {t('common.step')} {step} of 2
            </span>
            <span className="text-sm text-gray-500">
              {step === 1 ? t('register.step1Title') : t('register.step2Title')}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className="bg-purple-600 h-2 rounded-full"
              initial={{ width: '50%' }}
              animate={{ width: step === 1 ? '50%' : '100%' }}
              transition={{ duration: 0.3 }}
            />
          </div>
        </div>

        {/* 通用错误信息 */}
        <AnimatePresence>
          {errors.general && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-4 p-3 bg-red-50 border border-red-200 rounded-xl flex items-center gap-3"
            >
              <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
              <p className="text-sm text-red-700">{errors.general}</p>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 注册表单 */}
        <form onSubmit={step === 2 ? handleSubmit : (e) => { e.preventDefault(); handleNextStep(); }}>
          <AnimatePresence mode="wait">
            {step === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
                className="space-y-4"
              >
                {/* 姓名输入 */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <EnhancedAuthInput
                    type="text"
                    label={t('common.firstName')}
                    placeholder={t('register.firstNamePlaceholder')}
                    value={formData.firstName}
                    onChange={handleInputChange('firstName')}
                    error={errors.firstName}
                    leftIcon={<User className="w-5 h-5" />}
                    required
                  />
                  <EnhancedAuthInput
                    type="text"
                    label={t('common.lastName')}
                    placeholder={t('register.lastNamePlaceholder')}
                    value={formData.lastName}
                    onChange={handleInputChange('lastName')}
                    error={errors.lastName}
                    required
                  />
                </div>

                {/* 邮箱输入 */}
                <EnhancedAuthInput
                  type="email"
                  label={t('common.email')}
                  placeholder={t('register.emailPlaceholder')}
                  value={formData.email}
                  onChange={handleInputChange('email')}
                  error={errors.email}
                  leftIcon={<Mail className="w-5 h-5" />}
                  required
                />

                {/* 公司输入 (可选) */}
                <EnhancedAuthInput
                  type="text"
                  label={t('common.company')}
                  placeholder={t('register.companyPlaceholder')}
                  value={formData.company}
                  onChange={handleInputChange('company')}
                  error={errors.company}
                  leftIcon={<Building className="w-5 h-5" />}
                />
              </motion.div>
            )}

            {step === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
                className="space-y-4"
              >
                {/* 密码输入 */}
                <EnhancedAuthInput
                  type="password"
                  label={t('common.password')}
                  placeholder={t('register.passwordPlaceholder')}
                  value={formData.password}
                  onChange={handleInputChange('password')}
                  error={errors.password}
                  leftIcon={<Lock className="w-5 h-5" />}
                  showPasswordToggle={true}
                  required
                />
                <PasswordStrengthIndicator password={formData.password} />

                {/* 确认密码 */}
                <EnhancedAuthInput
                  type="password"
                  label={t('common.confirmPassword')}
                  placeholder={t('register.confirmPasswordPlaceholder')}
                  value={formData.confirmPassword}
                  onChange={handleInputChange('confirmPassword')}
                  error={errors.confirmPassword}
                  leftIcon={<Lock className="w-5 h-5" />}
                  showPasswordToggle={false}
                  required
                />
                {formData.password && formData.confirmPassword && !errors.confirmPassword && (
                  <div className="flex items-center text-xs text-green-600 gap-1">
                    <CheckCircle className="w-3 h-3" />
                    <span>{t('register.passwordsMatch')}</span>
                  </div>
                )}

                {/* 条款和条件 */}
                <div className="space-y-3 pt-2">
                  <div className="relative">
                    <label className="flex items-start gap-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.acceptTerms}
                        onChange={handleInputChange('acceptTerms')}
                        className="mt-1 w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                      />
                      <span className="text-sm text-gray-600">
                        {t('register.acceptTerms')}{' '}
                        <Link to="/terms" className="text-purple-600 hover:underline">{t('register.termsOfService')}</Link>
                        {' '}{t('common.and')}{' '}
                        <Link to="/privacy" className="text-purple-600 hover:underline">{t('register.privacyPolicy')}</Link>
                      </span>
                    </label>
                    {errors.acceptTerms && <p className="text-xs text-red-500 mt-1 ml-6">{errors.acceptTerms}</p>}
                  </div>

                  <label className="flex items-start gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.acceptMarketing}
                      onChange={handleInputChange('acceptMarketing')}
                      className="mt-1 w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                    />
                    <span className="text-sm text-gray-600">{t('register.acceptMarketing')}</span>
                  </label>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* 按钮组 */}
          <div className="pt-4 space-y-4">
            <div className="flex items-center gap-4">
              {step === 2 && (
                <EnhancedAuthButton
                  type="button"
                  variant="secondary"
                  size="md"
                  onClick={handlePrevStep}
                  fullWidth={true}
                >
                  {t('common.back')}
                </EnhancedAuthButton>
              )}
              <EnhancedAuthButton
                type={step === 1 ? 'button' : 'submit'}
                onClick={step === 1 ? handleNextStep : undefined}
                variant="primary"
                size="md"
                loading={loading}
                fullWidth={true}
                gradient={true}
                disabled={loading || googleLoading}
              >
                {step === 1 ? t('common.next') : (loading ? t('register.creatingAccount') : t('register.createAccountButton'))}
              </EnhancedAuthButton>
            </div>
            
            {/* 分隔线 */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white text-gray-500">{t('common.or')}</span>
              </div>
            </div>

            {/* Google注册按钮 */}
            <GoogleSignInButton
              loading={googleLoading}
              onGoogleSignIn={handleGoogleRegister}
              disabled={loading || googleLoading}
              variant="default"
              size="md"
              fullWidth={true}
            >
              {t('register.googleSignUp')}
            </GoogleSignInButton>
          </div>
        </form>

        {/* 登录链接 */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            {t('register.haveAccount')}{' '}
            <Link
              to="/login"
              className="text-purple-600 hover:text-purple-700 font-semibold transition-colors duration-200"
            >
              {t('register.signIn')}
            </Link>
          </p>
        </div>
      </motion.div>
    </EnhancedAuthLayout>
  );
}
